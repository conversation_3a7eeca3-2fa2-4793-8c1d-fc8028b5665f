import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Users, Mail, Key, Loader2, CheckCircle, XCircle } from 'lucide-react';
import { toast } from 'sonner';
import companyCodeService from '@/services/api/companyCodeService';
import companyInvitationService from '@/services/api/companyInvitationService';
import { CompanyCodeValidation, InvitationValidation } from '@/types/api';

interface JoinCompanyFormProps {
  onSuccess: () => void;
  onBack: () => void;
}

const JoinCompanyForm = ({ onSuccess, onBack }: JoinCompanyFormProps) => {
  const [loading, setLoading] = useState(false);
  const [validating, setValidating] = useState(false);
  
  // Estado para código de empresa
  const [companyCode, setCompanyCode] = useState('');
  const [codeValidation, setCodeValidation] = useState<CompanyCodeValidation | null>(null);
  
  // Estado para convite
  const [invitationToken, setInvitationToken] = useState('');
  const [invitationValidation, setInvitationValidation] = useState<InvitationValidation | null>(null);

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    setCompanyCode(value);
    setCodeValidation(null);
  };

  const handleTokenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInvitationToken(e.target.value);
    setInvitationValidation(null);
  };

  const validateCode = async () => {
    if (!companyCode.trim()) {
      toast.error('Digite um código válido');
      return;
    }

    setValidating(true);
    try {
      const response = await companyCodeService.validateCode(companyCode);
      setCodeValidation(response.data);
      
      if (!response.data.isValid) {
        toast.error(response.data.errorMessage || 'Código inválido');
      }
    } catch (error: any) {
      toast.error('Erro ao validar código');
      setCodeValidation({
        code: companyCode,
        isValid: false,
        errorMessage: 'Erro ao validar código',
      });
    } finally {
      setValidating(false);
    }
  };

  const validateInvitation = async () => {
    if (!invitationToken.trim()) {
      toast.error('Digite um token válido');
      return;
    }

    setValidating(true);
    try {
      const response = await companyInvitationService.validateInvitation(invitationToken);
      setInvitationValidation(response.data);
      
      if (!response.data.isValid) {
        toast.error(response.data.errorMessage || 'Convite inválido');
      }
    } catch (error: any) {
      toast.error('Erro ao validar convite');
      setInvitationValidation({
        token: invitationToken,
        isValid: false,
        errorMessage: 'Erro ao validar convite',
      });
    } finally {
      setValidating(false);
    }
  };

  const joinByCode = async () => {
    if (!codeValidation?.isValid) {
      toast.error('Valide o código primeiro');
      return;
    }

    setLoading(true);
    try {
      await companyCodeService.joinCompanyByCode({ code: companyCode });
      toast.success('Associado à empresa com sucesso!');
      onSuccess();
    } catch (error: any) {
      toast.error(
        error.response?.data?.message || 
        'Erro ao associar-se à empresa. Tente novamente.'
      );
    } finally {
      setLoading(false);
    }
  };

  const acceptInvitation = async () => {
    if (!invitationValidation?.isValid) {
      toast.error('Valide o convite primeiro');
      return;
    }

    setLoading(true);
    try {
      await companyInvitationService.acceptInvitation({ token: invitationToken });
      toast.success('Convite aceito com sucesso!');
      onSuccess();
    } catch (error: any) {
      toast.error(
        error.response?.data?.message || 
        'Erro ao aceitar convite. Tente novamente.'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="mx-auto max-w-2xl">
      <CardHeader>
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onBack}
            disabled={loading}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <CardTitle className="text-2xl font-bold flex items-center gap-2">
              <Users className="h-6 w-6 text-green-600" />
              Associar-se a Empresa
            </CardTitle>
            <CardDescription>
              Use um código de empresa ou aceite um convite por email
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="code" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="code" className="flex items-center gap-2">
              <Key className="h-4 w-4" />
              Código da Empresa
            </TabsTrigger>
            <TabsTrigger value="invitation" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Convite por Email
            </TabsTrigger>
          </TabsList>

          <TabsContent value="code" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="code">Código da Empresa</Label>
              <div className="flex gap-2">
                <Input
                  id="code"
                  type="text"
                  placeholder="Ex: ABC123XY"
                  value={companyCode}
                  onChange={handleCodeChange}
                  disabled={loading || validating}
                  maxLength={20}
                  className="uppercase"
                />
                <Button 
                  type="button"
                  variant="outline"
                  onClick={validateCode}
                  disabled={!companyCode.trim() || validating || loading}
                >
                  {validating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    'Validar'
                  )}
                </Button>
              </div>
              <p className="text-sm text-gray-600">
                Digite o código fornecido pelo administrador da empresa
              </p>
            </div>

            {codeValidation && (
              <div className={`p-4 rounded-lg border ${
                codeValidation.isValid 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center gap-2">
                  {codeValidation.isValid ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                  <span className={`font-medium ${
                    codeValidation.isValid ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {codeValidation.isValid ? 'Código válido!' : 'Código inválido'}
                  </span>
                </div>
                {codeValidation.isValid ? (
                  <div className="mt-2 text-sm text-green-700">
                    <p><strong>Empresa:</strong> {codeValidation.companyName}</p>
                    <p><strong>Função:</strong> {codeValidation.roleName}</p>
                  </div>
                ) : (
                  <p className="mt-2 text-sm text-red-700">
                    {codeValidation.errorMessage}
                  </p>
                )}
              </div>
            )}

            <div className="flex gap-4 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onBack}
                disabled={loading}
                className="flex-1"
              >
                Voltar
              </Button>
              <Button 
                type="button"
                onClick={joinByCode}
                disabled={!codeValidation?.isValid || loading}
                className="flex-1"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Associando...
                  </>
                ) : (
                  'Associar-se'
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="invitation" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="token">Token do Convite</Label>
              <div className="flex gap-2">
                <Input
                  id="token"
                  type="text"
                  placeholder="Cole aqui o token do convite"
                  value={invitationToken}
                  onChange={handleTokenChange}
                  disabled={loading || validating}
                />
                <Button 
                  type="button"
                  variant="outline"
                  onClick={validateInvitation}
                  disabled={!invitationToken.trim() || validating || loading}
                >
                  {validating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    'Validar'
                  )}
                </Button>
              </div>
              <p className="text-sm text-gray-600">
                Cole o token do convite que você recebeu por email
              </p>
            </div>

            {invitationValidation && (
              <div className={`p-4 rounded-lg border ${
                invitationValidation.isValid 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center gap-2">
                  {invitationValidation.isValid ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                  <span className={`font-medium ${
                    invitationValidation.isValid ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {invitationValidation.isValid ? 'Convite válido!' : 'Convite inválido'}
                  </span>
                </div>
                {invitationValidation.isValid ? (
                  <div className="mt-2 text-sm text-green-700">
                    <p><strong>Empresa:</strong> {invitationValidation.companyName}</p>
                    <p><strong>Função:</strong> {invitationValidation.roleName}</p>
                    <p><strong>Email:</strong> {invitationValidation.email}</p>
                  </div>
                ) : (
                  <p className="mt-2 text-sm text-red-700">
                    {invitationValidation.errorMessage}
                  </p>
                )}
              </div>
            )}

            <div className="flex gap-4 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onBack}
                disabled={loading}
                className="flex-1"
              >
                Voltar
              </Button>
              <Button 
                type="button"
                onClick={acceptInvitation}
                disabled={!invitationValidation?.isValid || loading}
                className="flex-1"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Aceitando...
                  </>
                ) : (
                  'Aceitar Convite'
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default JoinCompanyForm;
