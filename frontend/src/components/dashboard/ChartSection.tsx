import { useState, useEffect } from "react";
import { Area, AreaChart, Bar, BarChart, CartesianGrid, Cell, Legend, Pie, <PERSON>hart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";
import GlassCard from "../ui-custom/GlassCard";

interface ChartSectionProps {
  selectedPeriod: string;
  selectedAccountId: string;
}

// TODO: Implementar busca de dados de fluxo de caixa via API
const generateCashFlowData = (period: string, accountId: string) => {
  // Retornar dados vazios até implementar API
  return [];
};

// TODO: Implementar busca de dados de evolução do fluxo de caixa via API
const generateCashFlowEvolutionData = (period: string, accountId: string) => {
  // Retornar dados vazios até implementar API
  return [];
};

// TODO: Implementar busca de dados de despesas via API
const generateExpensesData = (period: string, accountId: string) => {
  // Retornar dados vazios até implementar API
  return [];
};

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#A259FF"];

interface ChartTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const ChartTabs = ({ activeTab, setActiveTab }: ChartTabsProps) => {
  const tabs = [
    { id: "cash-flow", label: "Fluxo de Caixa" },
    { id: "cash-flow-evolution", label: "Evolução do Saldo" },
  ];

  return (
    <div className="flex space-x-2 mb-4">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => setActiveTab(tab.id)}
          className={`
            rounded-lg transition-colors 
            font-medium text-xs leading-4 px-3 py-1.5
            font-sans
            ${
              activeTab === tab.id
                ? "bg-primary text-primary-foreground"
                : `bg-[#F8FAFC] dark:bg-gray-700 text-muted-foreground dark:text-gray-200 ${activeTab !== tab.id ? "hover:bg-secondary/50 dark:hover:bg-gray-600" : ""}`
            }
          `}
        >
          {tab.label}
        </button>
      ))}
    </div>
  );
};

const CashFlowChart = ({ cashFlowData }: { cashFlowData: any[] }) => {
  if (!cashFlowData || cashFlowData.length === 0) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground text-center">
          Nenhum dado de fluxo de caixa encontrado para o período selecionado.<br />
          Registre suas transações para visualizar o gráfico.
        </p>
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart
        data={cashFlowData}
        margin={{ top: 10, right: 20, left: 0, bottom: 0 }}
        className="animate-fade-in"
      >
        <defs>
          <linearGradient id="colorIncome" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#0088FE" stopOpacity={0.1} />
            <stop offset="95%" stopColor="#0088FE" stopOpacity={0.01} />
          </linearGradient>
          <linearGradient id="colorExpenses" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#FF8042" stopOpacity={0.1} />
            <stop offset="95%" stopColor="#FF8042" stopOpacity={0.01} />
          </linearGradient>
        </defs>
        <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.2} vertical={false} />
        <XAxis 
          dataKey="month" 
          axisLine={false} 
          tickLine={false}
          tick={{ fontSize: 12, fill: '#888' }}
        />
        <YAxis 
          axisLine={false} 
          tickLine={false} 
          tick={{ fontSize: 12, fill: '#888' }}
          tickFormatter={(value) => `R$${value / 1000}k`}
        />
        <Tooltip 
          contentStyle={{ 
            borderRadius: '10px', 
            border: '1px solid rgba(255,255,255,0.2)',
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            backgroundColor: 'rgba(255,255,255,0.9)'
          }} 
          formatter={(value) => [`R$${value.toLocaleString('pt-BR')}`, undefined]}
        />
        <Area 
          type="monotone" 
          dataKey="income" 
          stroke="#0088FE" 
          strokeWidth={2}
          fill="url(#colorIncome)" 
          activeDot={{ r: 6, strokeWidth: 0 }}
        />
        <Area 
          type="monotone" 
          dataKey="expenses" 
          stroke="#FF8042" 
          strokeWidth={2}
          fill="url(#colorExpenses)" 
          activeDot={{ r: 6, strokeWidth: 0 }}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

const CashFlowEvolutionChart = ({ cashFlowEvolutionData }: { cashFlowEvolutionData: any[] }) => {
  if (!cashFlowEvolutionData || cashFlowEvolutionData.length === 0) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground text-center">
          Nenhum dado de evolução do saldo encontrado para o período selecionado.<br />
          Registre suas transações para visualizar o gráfico.
        </p>
      </div>
    );
  }

  return (
    <div className="animate-fade-in">
      <ResponsiveContainer width="100%" height={300}>
        <AreaChart
          data={cashFlowEvolutionData}
          margin={{ top: 10, right: 20, left: 0, bottom: 0 }}
        >
          <defs>
            <linearGradient id="colorEvolution" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#0088FE" stopOpacity={0.3} />
              <stop offset="95%" stopColor="#0088FE" stopOpacity={0.1} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.2} vertical={false} />
          <XAxis 
            dataKey="month" 
            axisLine={false} 
            tickLine={false}
            tick={{ fontSize: 12, fill: '#888' }}
          />
          <YAxis 
            axisLine={false} 
            tickLine={false} 
            tick={{ fontSize: 12, fill: '#888' }}
            tickFormatter={(value) => `R$${value / 1000}k`}
          />
          <Tooltip 
            contentStyle={{ 
              borderRadius: '10px', 
              border: '1px solid rgba(255,255,255,0.2)',
              boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
              backgroundColor: 'rgba(255,255,255,0.9)'
            }} 
            formatter={(value) => [`R$${value.toLocaleString('pt-BR')}`, 'Saldo acumulado']}
          />
          <Area 
            type="monotone" 
            dataKey="value" 
            stroke="#0088FE" 
            strokeWidth={2}
            fill="url(#colorEvolution)" 
            activeDot={{ r: 6, strokeWidth: 0 }}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

const ChartSection = ({ selectedPeriod, selectedAccountId }: ChartSectionProps) => {
  const [activeTab, setActiveTab] = useState("cash-flow");
  const [cashFlowData, setCashFlowData] = useState<any[]>([]);
  const [cashFlowEvolutionData, setCashFlowEvolutionData] = useState<any[]>([]);
  
  // Atualizar dados quando os filtros mudarem
  useEffect(() => {
    setCashFlowData(generateCashFlowData(selectedPeriod, selectedAccountId));
    setCashFlowEvolutionData(generateCashFlowEvolutionData(selectedPeriod, selectedAccountId));
  }, [selectedPeriod, selectedAccountId]);

  return (
    <GlassCard className="h-full">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Análise Financeira</h2>
        <ChartTabs activeTab={activeTab} setActiveTab={setActiveTab} />
      </div>
      
      {activeTab === "cash-flow" ? 
        <CashFlowChart cashFlowData={cashFlowData} /> : 
        <CashFlowEvolutionChart cashFlowEvolutionData={cashFlowEvolutionData} />
      }
    </GlassCard>
  );
};

export default ChartSection;
