
import { useEffect, useState } from "react";
import { Bar, BarChart, CartesianGrid, Cell, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";
import GlassCard from "../ui-custom/GlassCard";

interface BudgetAnalysisProps {
  selectedPeriod: string;
  selectedAccountId: string;
}

interface BudgetData {
  name: string;
  planejado: number;
  realizado: number;
  percentual: number;
}

const BudgetAnalysis = ({ selectedPeriod, selectedAccountId }: BudgetAnalysisProps) => {
  const [data, setData] = useState<BudgetData[]>([]);
  
  useEffect(() => {
    // TODO: Implementar busca de dados reais de orçamento via API
    // const loadBudgetData = async () => {
    //   try {
    //     const budgetData = await fetchBudgetAnalysis({
    //       period: selectedPeriod,
    //       accountId: selectedAccountId
    //     });
    //     setData(budgetData);
    //   } catch (error) {
    //     console.error('Erro ao carregar dados de orçamento:', error);
    //     setData([]);
    //   }
    // };
    // loadBudgetData();

    // Temporariamente, definir dados vazios até implementar API
    setData([]);
  }, [selectedPeriod, selectedAccountId]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const getBarColor = (percentual: number) => {
    if (percentual <= 90) return "#10b981"; // Abaixo do orçamento (bom)
    if (percentual <= 100) return "#f59e0b"; // No orçamento
    return "#ef4444"; // Acima do orçamento (ruim)
  };

  // Mostrar estado vazio quando não há dados
  if (!data || data.length === 0) {
    return (
      <GlassCard className="relative overflow-hidden border-cyan-200/30 dark:border-cyan-800/30">
        <div className="absolute -right-8 -top-8 w-24 h-24 bg-gradient-to-br from-cyan-200/20 to-blue-200/20 rounded-full blur-xl"></div>
        <div className="relative z-10">
          <h2 className="font-semibold mb-4 flex items-center">
            <span className="inline-block w-3 h-3 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-full mr-2"></span>
            Análise Orçamentária
          </h2>
          <div className="h-[260px] flex items-center justify-center">
            <p className="text-muted-foreground text-center">
              Nenhum dado orçamentário encontrado para o período selecionado.<br />
              Configure seus orçamentos para visualizar a análise.
            </p>
          </div>
        </div>
      </GlassCard>
    );
  }

  return (
    <GlassCard className="relative overflow-hidden border-cyan-200/30 dark:border-cyan-800/30">
      <div className="absolute -right-8 -top-8 w-24 h-24 bg-gradient-to-br from-cyan-200/20 to-blue-200/20 rounded-full blur-xl"></div>
      <div className="relative z-10">
        <h2 className="font-semibold mb-4 flex items-center">
          <span className="inline-block w-3 h-3 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-full mr-2"></span>
          Análise Orçamentária
        </h2>
        <div className="h-[260px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              layout="vertical"
              margin={{ top: 5, right: 30, left: 70, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} opacity={0.3} />
              <XAxis 
                type="number" 
                tickFormatter={(value) => `${value}%`}
                domain={[0, 120] as any}
                axisLine={false}
                tickLine={false}
              />
              <YAxis 
                dataKey="name" 
                type="category" 
                axisLine={false}
                tickLine={false}
                tick={{ fill: '#666', fontSize: 12 }}
              />
              <Tooltip 
                formatter={(value, name, props) => {
                  if (name === 'percentual') {
                    return [`${value}%`, 'Realizado/Planejado'];
                  }
                  return [formatCurrency(value as number), name === 'planejado' ? 'Planejado' : 'Realizado'];
                }}
                contentStyle={{
                  borderRadius: '8px',
                  border: '1px solid rgba(0,0,0,0.1)',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                }}
              />
              <Bar 
                dataKey="percentual" 
                barSize={20}
                animationDuration={800}
              >
                {data.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={getBarColor(entry.percentual)} 
                    radius={4}
                    className="drop-shadow-sm"
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </GlassCard>
  );
};

export default BudgetAnalysis;
