
import { useEffect, useState } from "react";
import { Bar, Bar<PERSON>hart, CartesianGrid, Cell, Legend, ResponsiveContainer, Tooltip, XAxis, YAxi<PERSON> } from "recharts";
import GlassCard from "../ui-custom/GlassCard";

interface ProjectPerformanceProps {
  selectedPeriod: string;
  selectedAccountId: string;
}

interface ProjectData {
  name: string;
  budget: number;
  atual: number;
  performance: number;
}

const ProjectPerformance = ({ selectedPeriod, selectedAccountId }: ProjectPerformanceProps) => {
  const [data, setData] = useState<ProjectData[]>([]);
  
  useEffect(() => {
    // TODO: Implementar busca de dados reais de projetos via API
    // const loadProjectData = async () => {
    //   try {
    //     const projectData = await fetchProjectPerformance({
    //       period: selectedPeriod,
    //       accountId: selectedAccountId
    //     });
    //     setData(projectData);
    //   } catch (error) {
    //     console.error('Erro ao carregar dados de projetos:', error);
    //     setData([]);
    //   }
    // };
    // loadProjectData();

    // Temporariamente, definir dados vazios até implementar API
    setData([]);
  }, [selectedPeriod, selectedAccountId]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const getBarColor = (performance: number) => {
    if (performance <= 90) return "#10b981"; // Abaixo do orçamento (bom)
    if (performance <= 100) return "#f59e0b"; // No orçamento
    return "#ef4444"; // Acima do orçamento (ruim)
  };

  // Mostrar estado vazio quando não há dados
  if (!data || data.length === 0) {
    return (
      <GlassCard className="animate-fade-in">
        <h2 className="font-semibold mb-6">Performance de Projetos</h2>
        <div className="h-[350px] flex items-center justify-center">
          <p className="text-muted-foreground text-center">
            Nenhum projeto encontrado para o período selecionado.<br />
            Comece criando seus primeiros projetos.
          </p>
        </div>
      </GlassCard>
    );
  }

  return (
    <GlassCard className="animate-fade-in">
      <h2 className="font-semibold mb-6">Performance de Projetos</h2>
      <div className="h-[350px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis 
              dataKey="name"
              axisLine={false}
              tickLine={false}
            />
            <YAxis 
              yAxisId="left"
              orientation="left"
              tickFormatter={(value) => formatCurrency(value)}
              axisLine={false}
              tickLine={false}
            />
            <YAxis 
              yAxisId="right"
              orientation="right"
              tickFormatter={(value) => `${value}%`}
              domain={[0, 150]}
              axisLine={false}
              tickLine={false}
            />
            <Tooltip 
              formatter={(value, name) => {
                if (name === 'performance') {
                  return [`${value}%`, 'Performance'];
                }
                return [formatCurrency(value as number), name === 'budget' ? 'Orçamento' : 'Gasto Atual'];
              }}
              contentStyle={{
                borderRadius: '8px',
                border: '1px solid rgba(0,0,0,0.1)',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
              }}
            />
            <Legend />
            <Bar 
              yAxisId="left"
              dataKey="budget" 
              fill="#8884d8" 
              name="Orçamento"
              radius={[4, 4, 0, 0]}
            />
            <Bar 
              yAxisId="left"
              dataKey="atual" 
              fill="#82ca9d" 
              name="Gasto Atual"
              radius={[4, 4, 0, 0]}
            />
            <Bar 
              yAxisId="right"
              dataKey="performance" 
              fill="#ffc658" 
              name="Performance (%)"
              radius={[4, 4, 0, 0]}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={getBarColor(entry.performance)} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </GlassCard>
  );
};

export default ProjectPerformance;
