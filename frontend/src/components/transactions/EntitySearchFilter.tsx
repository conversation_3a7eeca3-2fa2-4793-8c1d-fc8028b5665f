
import { useState } from "react";
import { Building, Search, X, Check } from "lucide-react";
import { 
  Command, 
  CommandInput, 
  CommandEmpty, 
  CommandGroup, 
  CommandItem, 
  CommandList 
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

type Entity = {
  id: string;
  name: string;
  type?: string;
};

interface EntitySearchFilterProps {
  selectedEntityId: string;
  onEntityChange: (entityId: string) => void;
  entityType?: 'customer' | 'supplier' | undefined;
}

export default function EntitySearchFilter({
  selectedEntityId,
  onEntityChange,
  entityType
}: EntitySearchFilterProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  
  // TODO: Implementar busca de entidades via API
  // const { data: entities, loading } = useEntities({ type: entityType });
  const entities: Entity[] = [
    { id: "all", name: "Todas as entidades" }
  ];

  // Filter entities by type if specified
  const filteredEntitiesByType = entityType 
    ? [entities[0], ...entities.filter(entity => entity.type === entityType)]
    : entities;

  // Then filter by search query
  const filteredEntities = filteredEntitiesByType.filter(entity => 
    entity.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectedEntity = entities.find(entity => entity.id === selectedEntityId) || 
    (selectedEntityId ? { id: selectedEntityId, name: "Entidade Selecionada" } : entities[0]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          role="combobox" 
          aria-expanded={open}
          className="w-[200px] h-9 justify-start"
        >
          <Building className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className="truncate">
            {selectedEntity?.name || "Todas as entidades"}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[250px] p-0">
        <Command>
          <CommandInput 
            placeholder="Buscar entidade..." 
            value={searchQuery}
            onValueChange={setSearchQuery}
            className="h-9"
          />
          <CommandList>
            <CommandEmpty>
              {entities.length <= 1
                ? "Nenhuma entidade cadastrada. Cadastre clientes ou fornecedores primeiro."
                : "Nenhuma entidade encontrada."
              }
            </CommandEmpty>
            <CommandGroup>
              {filteredEntities.map((entity) => (
                <CommandItem
                  key={entity.id}
                  value={entity.name}
                  onSelect={() => {
                    onEntityChange(entity.id);
                    setOpen(false);
                    setSearchQuery("");
                  }}
                  className={cn(
                    "flex items-center gap-2 cursor-pointer",
                    selectedEntityId === entity.id && "bg-accent"
                  )}
                >
                  <Check 
                    className={cn(
                      "h-4 w-4 opacity-0", 
                      selectedEntityId === entity.id && "opacity-100"
                    )}
                  />
                  {entity.name}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
