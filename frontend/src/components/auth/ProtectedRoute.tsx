import React, { useEffect, useState } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import monitoringService from '@/services/api/monitoringService';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiresAuth?: boolean;
}

/**
 * Componente para proteger rotas que requerem autenticação
 * @param children - Componentes filhos a serem renderizados se autenticado
 * @param requiresAuth - Se a rota requer autenticação (padrão: true)
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiresAuth = true
}) => {
  const { isAuthenticated, loading, checkAuthStatus, initialized, loadingProfile, user } = useAuth();
  const [checking, setChecking] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();


  // Variável para controlar se já estamos verificando a autenticação
  const [hasVerified, setHasVerified] = useState(false);

  useEffect(() => {
    // Evitar verificações repetidas para a mesma rota
    const currentPath = location.pathname;

    const verifyAuth = async () => {
      try {
        // Se já verificamos, não precisamos verificar novamente
        if (hasVerified) {
          // console.log('[ProtectedRoute] Já verificou autenticação, pulando');
          setChecking(false);
          return;
        }

        // console.log('[ProtectedRoute] Verificando autenticação para rota:', currentPath);
        // console.log('[ProtectedRoute] Estado atual de autenticação:', isAuthenticated ? 'Autenticado' : 'Não autenticado');

        // Verificar o status de autenticação apenas para rotas protegidas
        // e apenas se o usuário não estiver autenticado
        if (requiresAuth && !isAuthenticated && initialized) {
          // console.log('[ProtectedRoute] Rota requer autenticação, verificando status...');
          await checkAuthStatus();
        } else {
          // console.log('[ProtectedRoute] Não é necessário verificar autenticação');
        }

        // Marcar que já verificamos a autenticação
        setHasVerified(true);
      } catch (error) {
        // Erro durante a verificação de autenticação - registrar no monitoramento
        monitoringService.recordEvent('auth_verification_failed', {
          path: currentPath,
          error
        });
      } finally {
        setChecking(false);
      }
    };

    verifyAuth();

    // Limpar o estado hasVerified quando a rota mudar
    return () => {
      if (currentPath !== location.pathname) {
        setHasVerified(false);
      }
    };
  }, [checkAuthStatus, requiresAuth, location.pathname, isAuthenticated, hasVerified, initialized]);


  // Mostrar indicador de carregamento enquanto verifica autenticação, até que a inicialização seja concluída ou enquanto o perfil está sendo carregado
  if (loading || checking || !initialized || loadingProfile) {
    // console.log('[ProtectedRoute] Aguardando carregamento/inicialização:', {
      // loading,
      // checking,
      // initialized,
      // loadingProfile,
      // isAuthenticated,
      // path: location.pathname
    // });

    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          <p className="text-muted-foreground">Verificando autenticação...</p>
        </div>
      </div>
    );
  }


  // Redirecionar para login se não estiver autenticado e a rota requer autenticação
  // Apenas se não estiver carregando ou verificando, a inicialização foi concluída e o perfil não está sendo carregado
  if (!isAuthenticated && requiresAuth && !loading && !checking && initialized && !loadingProfile) {
    // console.log('[ProtectedRoute] Usuário não autenticado tentando acessar rota protegida:', location.pathname);
    // console.log('[ProtectedRoute] Redirecionando para login');

    monitoringService.recordEvent('auth_redirect', {
      from: location.pathname,
      to: '/login'
    });

    // Salvar a URL atual para redirecionar de volta após o login
    sessionStorage.setItem('redirectAfterLogin', location.pathname);

    // Usar navigate em vez de Navigate para evitar problemas de renderização
    setTimeout(() => {
      navigate('/login', { replace: true, state: { from: location } });
    }, 0);

    // Enquanto isso, mostrar um indicador de carregamento
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          <p className="text-muted-foreground">Redirecionando para login...</p>
        </div>
      </div>
    );
  }

  // Redirecionar para dashboard se estiver autenticado e a rota não requer autenticação (ex: login)
  // Apenas se a inicialização foi concluída e o perfil não está sendo carregado
  if (isAuthenticated && !requiresAuth && initialized && !loadingProfile) {
    // console.log('[ProtectedRoute] Usuário autenticado tentando acessar rota pública:', location.pathname);
    // console.log('[ProtectedRoute] Redirecionando para a página inicial');

    monitoringService.recordEvent('auth_redirect', {
      from: location.pathname,
      to: '/'
    });

    // Usar setTimeout para garantir que o redirecionamento ocorra após a renderização
    setTimeout(() => {
      navigate('/');
    }, 0);

    // Enquanto isso, mostrar um indicador de carregamento
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          <p className="text-muted-foreground">Redirecionando...</p>
        </div>
      </div>
    );
  }

  // Redirecionar para onboarding se o usuário estiver autenticado mas sem empresa
  // Exceto se já estiver na página de onboarding
  if (isAuthenticated && user?.status === 'sem_empresa' &&
      location.pathname !== '/onboarding' && initialized && !loadingProfile) {

    monitoringService.recordEvent('auth_redirect', {
      from: location.pathname,
      to: '/onboarding',
      reason: 'user_without_company'
    });

    setTimeout(() => {
      navigate('/onboarding', { replace: true });
    }, 0);

    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          <p className="text-muted-foreground">Redirecionando para configuração...</p>
        </div>
      </div>
    );
  }

  // Registrar acesso à rota protegida
  monitoringService.recordEvent('protected_route_access', {
    path: location.pathname
  });


  return <>{children}</>;
};

export default ProtectedRoute;
