
import {
  Table,
} from "@/components/ui/table";
import { useState, useMemo, useEffect } from "react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import ReceiptForm from "@/components/accounts-receivable/ReceiptForm";
import AccountsReceivableTableFilters from "./AccountsReceivableTableFilters";
import AccountsReceivableTableHeader from "./AccountsReceivableTableHeader";
import AccountsReceivableTableBody from "./AccountsReceivableTableBody";

interface AccountsReceivableTableProps {
  onEdit: (item: any) => void;
  onView: (item: any) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filter: string;
  onFilterChange: (filter: string) => void;
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
  selectedAccountId: string;
  onAccountChange: (accountId: string) => void;
  selectedEntityId: string;
  onEntityChange: (entityId: string) => void;
  accounts: Array<{ id: string; name: string }>;
  onClearFilters: () => void;
  onClearAllFilters: () => void;
  customDateRange?: {
    startDate?: Date;
    endDate?: Date;
  };
  onDateRangeChange?: (startDate?: Date, endDate?: Date) => void;
  data?: any[];
  loading?: boolean;
  error?: string;
}

export default function AccountsReceivableTable({
  onEdit,
  onView,
  searchTerm,
  setSearchTerm,
  filter,
  onFilterChange,
  selectedPeriod,
  onPeriodChange,
  selectedAccountId,
  onAccountChange,
  selectedEntityId,
  onEntityChange,
  accounts,
  onClearFilters,
  onClearAllFilters,
  customDateRange,
  onDateRangeChange,
  data = [],
  loading = false,
  error
}: AccountsReceivableTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [isReceiptModalOpen, setIsReceiptModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  const filteredData = useMemo(() => {
    return data.filter(item => {
      const matchesSearch =
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.entity.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category.toLowerCase().includes(searchTerm.toLowerCase());

      let matchesFilter = true;
      if (filter !== "all") {
        if (filter === "received") {
          matchesFilter = item.status === "received";
        } else if (filter === "overdue") {
          matchesFilter = new Date(item.dueDate) < new Date() && item.status !== "received";
        } else if (filter === "pending") {
          // Modified to include pending, overdue and partial statuses
          matchesFilter = ["pending", "overdue", "partial"].includes(item.status);
        } else {
          matchesFilter = item.status === filter;
        }
      }

      let matchesAccount = true;
      if (selectedAccountId !== "all") {
        matchesAccount = item.bankAccountId === selectedAccountId;
      }

      let matchesEntity = true;
      if (selectedEntityId !== "all") {
        matchesEntity = item.entityId === selectedEntityId;
      }

      let matchesPeriod = true;
      if (selectedPeriod === "custom" && customDateRange?.startDate && customDateRange?.endDate) {
        matchesPeriod =
          item.dueDate >= customDateRange.startDate &&
          item.dueDate <= customDateRange.endDate;
      }

      return matchesSearch && matchesFilter && matchesAccount && matchesEntity && matchesPeriod;
    });
  }, [data, searchTerm, filter, selectedPeriod, selectedAccountId, selectedEntityId, customDateRange]);

  const handleReceive = (item: any) => {
    setSelectedItem(item);
    setIsReceiptModalOpen(true);
  };

  const handleView = (item: any) => {
    onView(item);
  };

  // Mostrar estado de loading
  if (loading) {
    return (
      <Card className="overflow-hidden border shadow-sm">
        <div className="p-6">
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </Card>
    );
  }

  // Mostrar estado de erro
  if (error) {
    return (
      <Card className="overflow-hidden border shadow-sm">
        <div className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Erro ao carregar contas a receber: {error}
            </AlertDescription>
          </Alert>
        </div>
      </Card>
    );
  }

  // Mostrar estado vazio
  if (!data || data.length === 0) {
    return (
      <Card className="overflow-hidden border shadow-sm">
        <AccountsReceivableTableFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          filter={filter}
          onFilterChange={onFilterChange}
          selectedPeriod={selectedPeriod}
          onPeriodChange={onPeriodChange}
          selectedAccountId={selectedAccountId}
          onAccountChange={onAccountChange}
          selectedEntityId={selectedEntityId}
          onEntityChange={onEntityChange}
          accounts={accounts}
          onClearFilters={onClearFilters}
          onClearAllFilters={onClearAllFilters}
          customDateRange={customDateRange}
          onDateRangeChange={onDateRangeChange}
        />
        <div className="p-6 text-center">
          <p className="text-muted-foreground">
            Nenhuma conta a receber encontrada. Comece criando sua primeira conta a receber.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card className="overflow-hidden border shadow-sm">
        <AccountsReceivableTableFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          filter={filter}
          onFilterChange={onFilterChange}
          selectedPeriod={selectedPeriod}
          onPeriodChange={onPeriodChange}
          selectedAccountId={selectedAccountId}
          onAccountChange={onAccountChange}
          selectedEntityId={selectedEntityId}
          onEntityChange={onEntityChange}
          accounts={accounts}
          onClearFilters={onClearFilters}
          onClearAllFilters={onClearAllFilters}
          customDateRange={customDateRange}
          onDateRangeChange={onDateRangeChange}
        />

        <div className="overflow-x-auto">
          <Table>
            <AccountsReceivableTableHeader />
            <AccountsReceivableTableBody
              filteredData={filteredData}
              onEdit={onEdit}
              handleReceive={handleReceive}
              handleView={handleView}
            />
          </Table>
        </div>

        {filteredData.length > 0 && (
          <div className="p-4 border-t">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious href="#" />
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#" isActive>1</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#">2</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#">3</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext href="#" />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </Card>

      <ReceiptForm
        open={isReceiptModalOpen}
        onOpenChange={setIsReceiptModalOpen}
        transaction={selectedItem}
      />
    </>
  );
}
