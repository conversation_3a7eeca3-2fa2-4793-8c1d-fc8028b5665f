import { useState, useEffect } from 'react';
import { parseCurrencyToNumber, numberToFormattedString } from "@/utils/currencyUtils";
import { RefreshableField } from '../constants';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';

export interface FormData {
  description: string;
  entity: string;
  dueDate: Date;
  amount: string;
  paidAmount: string;
  status: string;
  category: string;
  project: string;
  bankAccount: string;
  notes: string;
  interestAmount: string;
  discountAmount: string;
  markAsPaid: boolean;
  installments: boolean;
  numberOfInstallments: string;
  installmentFrequency: string;
  firstInstallmentDate: Date;
  recurrence: string;
  paymentMethod: string;
  invoiceNumber: string;
}

export const useAccountsReceivableForm = (id?: string, mode?: string) => {
  const navigate = useNavigate();
  
  // Initial form state
  const [formData, setFormData] = useState<FormData>({
    description: "",
    entity: "",
    dueDate: new Date(),
    amount: "0,00",
    paidAmount: "0,00",
    status: "pending",
    category: "",
    project: "",
    bankAccount: "",
    notes: "",
    interestAmount: "0,00",
    discountAmount: "0,00",
    markAsPaid: false,
    installments: false,
    numberOfInstallments: "1",
    installmentFrequency: "monthly",
    firstInstallmentDate: new Date(),
    recurrence: "none",
    paymentMethod: "",
    invoiceNumber: "",
  });

  const [refreshingFields, setRefreshingFields] = useState<Record<RefreshableField, boolean>>({
    entity: false,
    category: false,
    project: false,
    bankAccount: false,
    paymentMethod: false,
    recurrence: false
  });
  
  const [openCalendar, setOpenCalendar] = useState(false);
  const [openInstallmentCalendar, setOpenInstallmentCalendar] = useState(false);

  const isReadOnly = mode === "view";
  const isEditing = mode === "edit";
  const isNew = mode === "new";

  // Load data if editing or viewing an existing account
  useEffect(() => {
    if (id && id !== "new") {
      // TODO: Implementar busca de dados via API
      // const loadAccountData = async () => {
      //   try {
      //     const foundAccount = await fetchAccountReceivable(id);
      //     setFormData({
      //       description: foundAccount.description || "",
      //       entity: foundAccount.entity || "",
      //       dueDate: foundAccount.dueDate || new Date(),
      //       amount: foundAccount.amount ? numberToFormattedString(foundAccount.amount) : "0,00",
      //       paidAmount: foundAccount.paidAmount ? numberToFormattedString(foundAccount.paidAmount) : "0,00",
      //       status: foundAccount.status || "pending",
      //       category: foundAccount.category || "",
      //       project: foundAccount.project || "",
      //       bankAccount: foundAccount.bankAccount || "",
      //       notes: foundAccount.notes || "",
      //       interestAmount: foundAccount.interestAmount ? numberToFormattedString(foundAccount.interestAmount) : "0,00",
      //       discountAmount: foundAccount.discountAmount ? numberToFormattedString(foundAccount.discountAmount) : "0,00",
      //       markAsPaid: foundAccount.status === "received" || false,
      //       installments: false,
      //       numberOfInstallments: "1",
      //       installmentFrequency: "monthly",
      //       firstInstallmentDate: new Date(),
      //       recurrence: foundAccount.recurrence || "none",
      //       paymentMethod: foundAccount.paymentMethod || "",
      //       invoiceNumber: foundAccount.invoiceNumber || "",
      //     });
      //   } catch (error) {
      //     console.error('Erro ao carregar conta a receber:', error);
      //   }
      // };
      // loadAccountData();
    } else {
      // Inicializar com valores padrão formatados corretamente
      setFormData(prev => ({
        ...prev,
        amount: "0,00",
        paidAmount: "0,00",
        interestAmount: "0,00",
        discountAmount: "0,00"
      }));
    }
  }, [id]);

  // Handle field changes
  const handleChange = (field: string, value: any) => {
    if (isReadOnly) return;
    
    setFormData({
      ...formData,
      [field]: value,
    });
    
    if (field === "markAsPaid") {
      setFormData(prev => ({
        ...prev,
        [field]: value,
        status: value ? "received" : "pending",
        paidAmount: value ? prev.amount : "0,00",
      }));
    }
  };

  // Calculate the final amount with interest and discount
  const finalAmount = () => {
    const amount = parseCurrencyToNumber(formData.amount) || 0;
    const interest = parseCurrencyToNumber(formData.interestAmount) || 0;
    const discount = parseCurrencyToNumber(formData.discountAmount) || 0;
    return numberToFormattedString(amount + interest - discount);
  };

  // Function to navigate back to the accounts receivable list
  const handleBack = () => {
    navigate("/accounts-receivable");
  };

  // Function to submit the form (create or update an account)
  const handleSubmit = () => {
    console.log("Submitting form data:", formData);
    toast({
      title: isEditing ? "Conta atualizada" : "Conta cadastrada",
      description: `A conta foi ${isEditing ? "atualizada" : "cadastrada"} com sucesso.`,
    });
    navigate("/accounts-receivable");
  };

  // Function to refresh field options
  const refreshFieldOptions = (field: RefreshableField) => {
    // Set only the specific field to refreshing state
    setRefreshingFields(prev => ({
      ...prev,
      [field]: true
    }));
    
    // Simulate API call delay
    setTimeout(() => {
      // Reset only the specific field refreshing state
      setRefreshingFields(prev => ({
        ...prev,
        [field]: false
      }));
      
      // Show success toast with the specific field name
      const fieldDisplayNames: Record<RefreshableField, string> = {
        entity: "clientes",
        category: "categorias",
        project: "projetos",
        bankAccount: "contas bancárias",
        paymentMethod: "métodos de pagamento",
        recurrence: "tipos de recorrência"
      };
      
      toast({
        title: "Opções atualizadas",
        description: `A lista de ${fieldDisplayNames[field]} foi atualizada com sucesso.`,
      });
    }, 800);
  };

  // Function to navigate to edit mode
  const handleEdit = (accountId: string) => {
    navigate(`/accounts-receivable/${accountId}/edit`);
  };

  return {
    formData,
    refreshingFields,
    openCalendar,
    setOpenCalendar,
    openInstallmentCalendar,
    setOpenInstallmentCalendar,
    isReadOnly,
    isEditing,
    isNew,
    handleChange,
    finalAmount,
    handleBack,
    handleSubmit,
    refreshFieldOptions,
    handleEdit,
  };
}; 