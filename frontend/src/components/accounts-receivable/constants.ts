// Status options for accounts receivable
export const statusOptions = [
  { label: "Pendente", value: "pending" },
  { label: "Parcial", value: "partial" },
  { label: "Recebido", value: "received" },
  { label: "Vencido", value: "overdue" },
];

// DEPRECATED: Use hooks usePaymentMethodOptions e useRecurrenceTypeOptions instead
// Mantido temporariamente para compatibilidade
export const recurrenceOptions = [
  { label: "Não recorrente", value: "none" },
  { label: "<PERSON><PERSON><PERSON>", value: "daily" },
  { label: "Semanal", value: "weekly" },
  { label: "Quinzenal", value: "biweekly" },
  { label: "Mensal", value: "monthly" },
  { label: "Bimestral", value: "bimonthly" },
  { label: "Trimestral", value: "quarterly" },
  { label: "Semestral", value: "semiannually" },
  { label: "Anual", value: "annually" },
];

// DEPRECATED: Use hook usePaymentMethodOptions instead
// Mantido temporariamente para compatibilidade
export const paymentMethodOptions = [
  { label: "Dinheiro", value: "cash" },
  { label: "Cartão de Crédito", value: "credit_card" },
  { label: "Cartão de Débito", value: "debit_card" },
  { label: "Transferência Bancária", value: "bank_transfer" },
  { label: "Boleto", value: "boleto" },
  { label: "Pix", value: "pix" },
  { label: "Cheque", value: "check" },
];

// Define field names for refreshing
export type RefreshableField = 
  | "entity" 
  | "category" 
  | "project" 
  | "bankAccount" 
  | "paymentMethod" 
  | "recurrence";

// Nota: Dados de categorias, projetos, clientes e contas bancárias
// devem ser obtidos através de hooks específicos que fazem chamadas à API