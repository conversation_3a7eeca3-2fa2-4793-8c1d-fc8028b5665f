import api from './axios';
import {
  CompanyInvitation,
  CreateCompanyInvitationRequest,
  AcceptInvitationRequest,
  InvitationValidation,
  PaginatedResponse,
  ApiResponse,
} from '@/types/api';

class CompanyInvitationService {
  private readonly baseUrl = '/company-invitations';

  /**
   * <PERSON><PERSON>r convite para empresa
   */
  async createInvitation(data: CreateCompanyInvitationRequest): Promise<ApiResponse<CompanyInvitation>> {
    const response = await api.post<ApiResponse<CompanyInvitation>>(this.baseUrl, data);
    return response.data;
  }

  /**
   * Validar token de convite
   */
  async validateInvitation(token: string): Promise<ApiResponse<InvitationValidation>> {
    const response = await api.get<ApiResponse<InvitationValidation>>(`${this.baseUrl}/validate/${token}`);
    return response.data;
  }

  /**
   * Aceitar convite de empresa
   */
  async acceptInvitation(data: AcceptInvitationRequest): Promise<ApiResponse<{ message: string }>> {
    const response = await api.post<ApiResponse<{ message: string }>>(`${this.baseUrl}/accept`, data);
    return response.data;
  }

  /**
   * Listar convites de uma empresa
   */
  async getCompanyInvitations(
    companyId: string,
    page = 1,
    limit = 10
  ): Promise<ApiResponse<PaginatedResponse<CompanyInvitation>>> {
    const response = await api.get<ApiResponse<PaginatedResponse<CompanyInvitation>>>(
      `${this.baseUrl}/company/${companyId}`,
      {
        params: { page, limit },
      }
    );
    return response.data;
  }

  /**
   * Revogar convite
   */
  async revokeInvitation(invitationId: string): Promise<ApiResponse<{ message: string }>> {
    const response = await api.delete<ApiResponse<{ message: string }>>(`${this.baseUrl}/${invitationId}`);
    return response.data;
  }
}

export default new CompanyInvitationService();
