import api from './axios';
import {
  CompanyCode,
  CreateCompanyCodeRequest,
  JoinCompanyByCodeRequest,
  UpdateCompanyCodeRequest,
  CompanyCodeValidation,
  PaginatedResponse,
  ApiResponse,
} from '@/types/api';

class CompanyCodeService {
  private readonly baseUrl = '/company-codes';

  /**
   * Criar código de empresa
   */
  async createCode(data: CreateCompanyCodeRequest): Promise<ApiResponse<CompanyCode>> {
    const response = await api.post<ApiResponse<CompanyCode>>(this.baseUrl, data);
    return response.data;
  }

  /**
   * Validar código de empresa
   */
  async validateCode(code: string): Promise<ApiResponse<CompanyCodeValidation>> {
    const response = await api.get<ApiResponse<CompanyCodeValidation>>(`${this.baseUrl}/validate/${code}`);
    return response.data;
  }

  /**
   * Associar-se à empresa usando código
   */
  async joinCompanyByCode(data: JoinCompanyByCodeRequest): Promise<ApiResponse<{ message: string }>> {
    const response = await api.post<ApiResponse<{ message: string }>>(`${this.baseUrl}/join`, data);
    return response.data;
  }

  /**
   * Listar códigos de uma empresa
   */
  async getCompanyCodes(
    companyId: string,
    page = 1,
    limit = 10
  ): Promise<ApiResponse<PaginatedResponse<CompanyCode>>> {
    const response = await api.get<ApiResponse<PaginatedResponse<CompanyCode>>>(
      `${this.baseUrl}/company/${companyId}`,
      {
        params: { page, limit },
      }
    );
    return response.data;
  }

  /**
   * Atualizar código de empresa
   */
  async updateCode(codeId: string, data: UpdateCompanyCodeRequest): Promise<ApiResponse<CompanyCode>> {
    const response = await api.put<ApiResponse<CompanyCode>>(`${this.baseUrl}/${codeId}`, data);
    return response.data;
  }

  /**
   * Remover código de empresa
   */
  async removeCode(codeId: string): Promise<ApiResponse<{ message: string }>> {
    const response = await api.delete<ApiResponse<{ message: string }>>(`${this.baseUrl}/${codeId}`);
    return response.data;
  }
}

export default new CompanyCodeService();
