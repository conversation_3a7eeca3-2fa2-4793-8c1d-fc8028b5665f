import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, LoginRequest, RegisterRequest, Company, UserStatusResponse } from '@/types/api';
import { authService } from '@/services/api/authService';
import { userService } from '@/services/api/userService';
import { companyService } from '@/services/api/companyService';
import tokenStorage from '@/services/api/tokenStorage';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { monitoringService, EventType } from '@/services/monitoring/monitoringService';
import { logger } from '@/utils/secureLogger';

type AuthContextType = {
  user: User | null;
  loading: boolean;
  companies: Company[] | null;
  activeCompanyId: string | null;
  login: (data: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string, passwordConfirmation: string) => Promise<void>;
  switchCompany: (companyId: string) => void;
  isAuthenticated: boolean;
  checkAuthStatus: () => Promise<boolean>;
  // Indica se a verificação de autenticação inicial foi concluída
  initialized: boolean;
  // Indica se o perfil do usuário está sendo carregado
  loadingProfile: boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Função simples para tratamento de erros dentro do AuthProvider
const handleAuthError = (error: any) => {
  // Erro de autenticação - processar sem log
  const message = error?.response?.data?.message || 'Ocorreu um erro inesperado.';
  toast.error(message);
  return { error, message };
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [companies, setCompanies] = useState<Company[] | null>(null);
  const [activeCompanyId, setActiveCompanyId] = useState<string | null>(localStorage.getItem('activeCompanyId'));
  const [loading, setLoading] = useState(true);
  // Estado para controlar se a verificação de autenticação inicial foi concluída
  const [initialized, setInitialized] = useState(false);
  // Estado para controlar se o perfil do usuário está sendo carregado
  const [loadingProfile, setLoadingProfile] = useState(false);
  // Estado explícito para controlar a autenticação
  // Inicializar com o valor do localStorage, se existir
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(
    localStorage.getItem('isAuthenticated') === 'true'
  );
  const navigate = useNavigate();

  // Função para registrar eventos de autenticação para fins de monitoramento
  // Agora usando o serviço de monitoramento baseado em eventos
  const logAuthEvent = (event: string, details?: any) => {

    // Mapear eventos do contexto para os tipos de eventos do serviço de monitoramento
    let eventType: EventType;
    switch (event) {
      case 'auth_check_success':
        eventType = EventType.AUTH_LOGIN;
        break;
      case 'auth_check_failed':
        eventType = EventType.AUTH_LOGIN_ERROR;
        break;
      case 'login_success':
        eventType = EventType.AUTH_LOGIN;
        break;
      case 'login_failed':
        eventType = EventType.AUTH_LOGIN_ERROR;
        break;
      case 'register_success':
        eventType = EventType.AUTH_REGISTER;
        break;
      case 'register_failed':
        eventType = EventType.AUTH_REGISTER_ERROR;
        break;
      case 'logout_success':
        eventType = EventType.AUTH_LOGOUT;
        break;
      case 'logout_error':
        eventType = EventType.AUTH_LOGOUT_ERROR;
        break;
      case 'forgot_password_request':
        eventType = EventType.AUTH_PASSWORD_RESET_REQUEST;
        break;
      case 'forgot_password_failed':
        eventType = EventType.AUTH_PASSWORD_RESET_ERROR;
        break;
      case 'password_reset_success':
        eventType = EventType.AUTH_PASSWORD_RESET;
        break;
      case 'password_reset_failed':
        eventType = EventType.AUTH_PASSWORD_RESET_ERROR;
        break;
      default:
        eventType = EventType.ERROR;
    }

    // Enviar evento para o serviço de monitoramento
    monitoringService.recordAuthEvent(eventType, {
      ...details,
      action: event,
      success: !event.includes('failed') && !event.includes('error')
    });
  };

  // Variável para controlar se já estamos verificando a autenticação
  // Isso evita múltiplas verificações simultâneas
  const [isCheckingAuth, setIsCheckingAuth] = useState(false);

  // Função para verificar o status de autenticação
  const checkAuthStatus = async (): Promise<boolean> => {
    // Se já estamos verificando ou se o usuário já está autenticado, retornar imediatamente
    if (isCheckingAuth) {
      return !!user;
    }

    // Se o usuário já está autenticado, não precisamos verificar novamente
    if (user) {
      return true;
    }

    try {
      setIsCheckingAuth(true);
      setLoading(true);

      // Verificar se temos tokens armazenados
      if (!authService.tokenStorage.hasTokens()) {
        setUser(null);
        setCompanies(null);
        setActiveCompanyId(null);
        return false;
      }

      // Verificar se o token está expirado
      if (authService.tokenStorage.isTokenExpired()) {
        // Tentar renovar o token
        const refreshResult = await authService.refreshToken();

        if (!refreshResult) {
          setUser(null);
          setCompanies(null);
          setActiveCompanyId(null);
          return false;
        }

        setUser(refreshResult.user);

        // Buscar empresas do usuário
        try {
          const companiesData = await loadCompanies();
          setCompanies(companiesData);

          // Verificar se temos uma empresa ativa salva
          const savedCompanyId = localStorage.getItem('activeCompanyId');
          if (savedCompanyId && companiesData.some(company => company.id === savedCompanyId)) {
            setActiveCompanyId(savedCompanyId);
          } else if (companiesData.length > 0) {
            // Se não tiver empresa ativa ou ela não existir mais, usar a primeira
            setActiveCompanyId(companiesData[0].id);
            localStorage.setItem('activeCompanyId', companiesData[0].id);
          }
        } catch (error) {
          logger.error('Erro ao buscar empresas após renovação de token', error, { userId: user?.id });
        }

        return true;
      }

      // Token válido, buscar perfil do usuário
      try {
        // Definir o estado de carregamento do perfil
        setLoadingProfile(true);

        // Criar uma promessa com timeout para evitar que a chamada fique presa
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('Timeout ao buscar perfil do usuário'));
          }, 5000); // 5 segundos de timeout
        });

        // Buscar status do usuário (incluindo empresas associadas) com timeout
        const userStatusData = await Promise.race([
          authService.getUserStatus(),
          timeoutPromise
        ]);

        // Verificar se o usuário retornado é válido (tem ID)
        if (userStatusData && userStatusData.user && userStatusData.user.id) {
          // Atualizar o estado user e forçar a atualização do estado isAuthenticated
          setUser(userStatusData.user);
          setIsAuthenticated(true);

          // Se o usuário tem empresas associadas, carregar empresas
          if (userStatusData.hasCompany) {
            try {
              const companiesData = await loadCompanies();
              setCompanies(companiesData);

              // Verificar se temos uma empresa ativa salva
              const savedCompanyId = localStorage.getItem('activeCompanyId');
              if (savedCompanyId && companiesData.some(company => company.id === savedCompanyId)) {
                setActiveCompanyId(savedCompanyId);
              } else if (companiesData.length > 0) {
                // Se não tiver empresa ativa ou ela não existir mais, usar a primeira
                setActiveCompanyId(companiesData[0].id);
                localStorage.setItem('activeCompanyId', companiesData[0].id);
              }
            } catch (error) {
              logger.error('Erro ao buscar empresas após verificar perfil', error, { userId: userStatusData.user?.id });
            }
          } else {
            // Usuário sem empresa - limpar dados de empresa
            setCompanies([]);
            setActiveCompanyId(null);
            localStorage.removeItem('activeCompanyId');
          }

          return true;
        } else {
          logger.warn('Dados do usuário inválidos ou incompletos');
          setUser(null);
          setIsAuthenticated(false);
          return false;
        }
      } catch (error) {
        logger.error('Erro ao buscar perfil do usuário', error);
        setUser(null);
        setIsAuthenticated(false);
        return false; // Retornar false em vez de lançar erro
      } finally {
        // Garantir que o estado de carregamento do perfil seja definido como false
        setLoadingProfile(false);
      }

      logAuthEvent('auth_check_success');

      // Marcar que a verificação de autenticação inicial foi concluída
      setInitialized(true);

      return true;
    } catch (error) {
      logAuthEvent('auth_check_failed', { error });
      handleAuthError(error);
      setUser(null);
      setCompanies(null);
      setActiveCompanyId(null);
      return false;
    } finally {
      setLoading(false);
      setIsCheckingAuth(false);

      // Garantir que initialized seja definido como true mesmo em caso de erro
      // Isso evita que o usuário fique preso em um estado de carregamento
      setInitialized(true);
    }
  };

  // Verificar se os tokens existem e são válidos no início da aplicação
  const checkTokensOnStartup = () => {
    // Verificar se temos um estado de autenticação persistido
    const persistedAuth = localStorage.getItem('isAuthenticated') === 'true';

    // Verificar se temos tokens armazenados
    if (!authService.tokenStorage.hasTokens()) {
      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem('isAuthenticated');
      setInitialized(true);
      setLoading(false);
      return;
    }

    // Verificar se o token está expirado
    if (authService.tokenStorage.isTokenExpired()) {
      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem('isAuthenticated');
      setInitialized(true);
      setLoading(false);
      return;
    }

    // Se temos um estado de autenticação persistido, definir isAuthenticated como true
    // mesmo antes de verificar o perfil do usuário
    if (persistedAuth) {
      setIsAuthenticated(true);
    }

    // Se os tokens existem e são válidos, verificar o status de autenticação
    // Usar setTimeout para evitar chamadas recursivas e garantir que o estado seja atualizado
    setTimeout(() => {
      checkAuthStatus();
    }, 0);
  };

  // Verificar autenticação ao carregar a página
  useEffect(() => {
    checkTokensOnStartup();
  }, []);

  // Monitorar o estado initialized
  useEffect(() => {
  }, [initialized]);

  // Monitorar o estado isAuthenticated
  useEffect(() => {
  }, [isAuthenticated]);

  const login = async (data: LoginRequest) => {
    setLoading(true);
    try {
      logAuthEvent('login_attempt', { email: data.email });

      // Fazer login e obter token e perfil do usuário
      const response = await authService.login(data);

      // Definir usuário no estado
      setUser(response.user);

      // Garantir que initialized seja definido como true
      setInitialized(true);

      // Buscar empresas do usuário
      const companiesData = await companyService.getCompanies();

      // Verificar se a resposta tem a estrutura esperada
      if (!companiesData) {
        logger.error('Resposta de empresas inválida durante login', undefined, {
          hasData: !!companiesData,
          userId: response.user.id
        });
        setCompanies([]);
      } else {
        // A API retorna {items: [...], total, page, limit, totalPages}
        setCompanies(companiesData.data || []);
      }

      // Definir empresa ativa (primeira ou última usada)
      const savedCompanyId = localStorage.getItem('activeCompanyId');

      // Verificar se temos empresas válidas
      if (companiesData && companiesData.data && companiesData.data.length > 0) {
        if (savedCompanyId && companiesData.data.some(company => company.id === savedCompanyId)) {
          setActiveCompanyId(savedCompanyId);
        } else {
          setActiveCompanyId(companiesData.data[0].id);
          localStorage.setItem('activeCompanyId', companiesData.data[0].id);
        }
      } else {
        // Se não houver empresas, limpar o activeCompanyId
        setActiveCompanyId(null);
      }

      toast.success('Login realizado com sucesso!');
      logAuthEvent('login_success', { userId: response.user.id });

      // Redirecionar para a página inicial ou página anterior
      const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/';
      sessionStorage.removeItem('redirectAfterLogin');

      // Forçar a atualização do estado isAuthenticated antes de redirecionar
      setIsAuthenticated(true);

      // Armazenar o caminho de redirecionamento em uma variável local para evitar problemas de closure
      const finalRedirectPath = redirectPath;

      // Usar setTimeout com um atraso maior para garantir que o estado seja atualizado antes do redirecionamento
      setTimeout(() => {
        // Verificar novamente se o usuário ainda está autenticado antes de redirecionar
        if (user) {
          navigate(finalRedirectPath, { replace: true });
        }
      }, 300);
    } catch (error: any) {
      logAuthEvent('login_failed', { email: data.email, error: error.message });
      handleAuthError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (data: RegisterRequest) => {
    setLoading(true);
    try {
      // Remover passwordConfirmation antes de enviar para o backend
      const { passwordConfirmation, ...registerData } = data;
      await authService.register(registerData);
      toast.success('Conta criada com sucesso! Faça login para continuar.');
      logAuthEvent('register_success', { email: data.email });
      navigate('/login');
    } catch (error: any) {
      logAuthEvent('register_failed', { email: data.email, error: error.message });
      handleAuthError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    setLoading(true);
    try {
      logAuthEvent('logout_attempt', user ? { userId: user.id } : undefined);

      await authService.logout();
      logAuthEvent('logout_success', user ? { userId: user.id } : undefined);

      // Limpar estados
      setUser(null);
      setCompanies(null);
      setActiveCompanyId(null);

      // Garantir que os tokens sejam removidos
      tokenStorage.clearTokens();

      // Remover o estado de autenticação do localStorage
      localStorage.removeItem('isAuthenticated');

      toast.success('Logout realizado com sucesso!');
      navigate('/login');
    } catch (error) {
      logger.error('Erro ao fazer logout', error, { userId: user?.id });
      logAuthEvent('logout_error', { error });

      // Mesmo com erro, limpar o estado de autenticação e tokens
      setUser(null);
      setCompanies(null);
      setActiveCompanyId(null);
      tokenStorage.clearTokens();

      navigate('/login');
    } finally {
      setLoading(false);
    }
  };

  // Função para solicitar recuperação de senha
  const forgotPassword = async (email: string) => {
    setLoading(true);
    try {
      await authService.forgotPassword(email);
      logAuthEvent('forgot_password_request', { email });
      toast.success('Instruções de recuperação enviadas para seu e-mail');
    } catch (error) {
      logAuthEvent('forgot_password_failed', { email, error });
      handleAuthError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Função para redefinir senha
  const resetPassword = async (token: string, password: string, passwordConfirmation: string) => {
    setLoading(true);
    try {
      await authService.resetPassword(token, password, passwordConfirmation);
      logAuthEvent('password_reset_success');
      toast.success('Senha redefinida com sucesso! Faça login para continuar.');
      navigate('/login');
    } catch (error) {
      logAuthEvent('password_reset_failed', { error });
      handleAuthError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Função para trocar de empresa
  const switchCompany = (companyId: string) => {
    if (companies && companies.some(company => company.id === companyId)) {
      setActiveCompanyId(companyId);
      localStorage.setItem('activeCompanyId', companyId);
      toast.success('Empresa alterada com sucesso!');

      // Registrar evento de troca de empresa
      monitoringService.recordEvent('company_switch', {
        userId: user?.id,
        companyId
      });
    } else {
      toast.error('Empresa não encontrada');
    }
  };

  // Garantir que o activeCompanyId seja sempre sincronizado com o localStorage
  useEffect(() => {
    if (activeCompanyId) {
      localStorage.setItem('activeCompanyId', activeCompanyId);
    } else {
      localStorage.removeItem('activeCompanyId');
    }
  }, [activeCompanyId]);

  // Atualizar isAuthenticated sempre que o usuário mudar
  useEffect(() => {
    const authState = !!user;

    // Usar um timeout para garantir que o estado seja atualizado após todas as outras operações
    // Isso evita problemas de sincronização entre diferentes componentes
    setTimeout(() => {
      setIsAuthenticated(authState);

      // Persistir o estado de autenticação no localStorage
      if (authState) {
        localStorage.setItem('isAuthenticated', 'true');
      } else {
        localStorage.removeItem('isAuthenticated');
      }
    }, 0);
  }, [user]);

  // Função para carregar empresas do usuário
  const loadCompanies = async (): Promise<Company[]> => {
    try {
      const companiesData = await companyService.getCompanies(1, 100); // Buscar todas as empresas (limite alto)

      if (companiesData && companiesData.data) {
        return companiesData.data;
      }

      // Nenhuma empresa encontrada ou estrutura inválida
      return [];
    } catch (error) {
      // Registrar evento de erro
      monitoringService.recordEvent('auth_load_companies_error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: user?.id
      });

      logger.error('Erro ao carregar empresas do usuário', error, { userId: user?.id });
      throw error;
    }
  };

  const value = {
    user,
    companies,
    activeCompanyId,
    loading,
    login,
    register,
    logout,
    forgotPassword,
    resetPassword,
    switchCompany,
    isAuthenticated,
    checkAuthStatus,
    initialized,
    loadingProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};
