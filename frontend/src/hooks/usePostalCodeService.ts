
import { useState } from 'react';

// TypeScript interface for postal code data
export interface PostalCode {
  id: string;
  company_id?: string;
  zip_code: string;
  street: string;
  neighborhood: string;
  city: string;
  state: string;
  registration_origin: 'manual' | 'auto';
  created_at: string;
  updated_at: string;
}

// TODO: Implementar busca de códigos postais via API
// const { data: postalCodes, loading } = usePostalCodes();

export const usePostalCodeService = () => {
  const [postalCodes, setPostalCodes] = useState<PostalCode[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get all postal codes
  const getPostalCodes = async (): Promise<PostalCode[]> => {
    // Simulate API request
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(postalCodes);
      }, 500);
    });
  };

  // Get a postal code by ID
  const getPostalCodeById = async (id: string): Promise<PostalCode | undefined> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const postalCode = postalCodes.find(pc => pc.id === id);
        resolve(postalCode);
      }, 300);
    });
  };

  // Check if a postal code (CEP) already exists
  const checkPostalCodeExists = async (zipCode: string): Promise<boolean> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const exists = postalCodes.some(pc => 
          pc.zip_code.replace(/[^0-9]/g, '') === zipCode.replace(/[^0-9]/g, '')
        );
        resolve(exists);
      }, 300);
    });
  };

  // Save a postal code (create or update)
  const savePostalCode = async (postalCode: Partial<PostalCode>): Promise<PostalCode> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const now = new Date().toISOString();
        
        // Clean the zip code format
        const cleanZipCode = postalCode.zip_code?.replace(/[^0-9]/g, '') || '';
        
        // Format the zip code as 00000-000
        const formattedZipCode = cleanZipCode.length === 8 
          ? `${cleanZipCode.substring(0, 5)}-${cleanZipCode.substring(5)}`
          : postalCode.zip_code || '';
        
        // Preserve the origin type - only set if it's a new record without an origin
        const origin = postalCode.registration_origin || 'manual';
        
        // Check if we're updating an existing postal code or creating a new one
        if (postalCode.id && postalCodes.some(pc => pc.id === postalCode.id)) {
          // Update existing postal code
          const updatedPostalCodes = postalCodes.map(pc => 
            pc.id === postalCode.id 
              ? { 
                  ...pc, 
                  ...postalCode, 
                  zip_code: formattedZipCode,
                  registration_origin: pc.registration_origin, // Preserve the original origin
                  updated_at: now 
                } 
              : pc
          );
          
          setPostalCodes(updatedPostalCodes);
          
          const updatedPostalCode = updatedPostalCodes.find(pc => pc.id === postalCode.id);
          if (updatedPostalCode) {
            resolve(updatedPostalCode);
          } else {
            reject(new Error('Failed to update postal code'));
          }
        } else {
          // Create new postal code
          const newPostalCode: PostalCode = {
            id: postalCode.id || crypto.randomUUID(),
            company_id: postalCode.company_id || '1', // Default company ID
            zip_code: formattedZipCode,
            street: postalCode.street || '',
            neighborhood: postalCode.neighborhood || '',
            city: postalCode.city || '',
            state: postalCode.state || '',
            registration_origin: origin, // Use the provided origin or default to manual
            created_at: now,
            updated_at: now,
          };
          
          setPostalCodes([...postalCodes, newPostalCode]);
          resolve(newPostalCode);
        }
      }, 500);
    });
  };

  // Delete a postal code
  const deletePostalCode = async (id: string): Promise<void> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        setPostalCodes(postalCodes.filter(pc => pc.id !== id));
        resolve();
      }, 500);
    });
  };

  // Search postal codes
  const searchPostalCodes = async (term: string): Promise<PostalCode[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const searchTerm = term.toLowerCase();
        const results = postalCodes.filter(pc => 
          pc.zip_code.toLowerCase().includes(searchTerm) ||
          pc.street.toLowerCase().includes(searchTerm) ||
          pc.neighborhood.toLowerCase().includes(searchTerm) ||
          pc.city.toLowerCase().includes(searchTerm) ||
          pc.state.toLowerCase().includes(searchTerm)
        );
        resolve(results);
      }, 300);
    });
  };

  // Fetch address data from ViaCEP API
  const fetchFromViaCEP = async (zipCode: string): Promise<Partial<PostalCode> | null> => {
    try {
      // Clean zip code format for API call
      const cleanZipCode = zipCode.replace(/[^0-9]/g, '');
      
      if (cleanZipCode.length !== 8) {
        return null;
      }
      
      const response = await fetch(`https://viacep.com.br/ws/${cleanZipCode}/json/`);
      const data = await response.json();
      
      if (data.erro) {
        return null;
      }
      
      return {
        zip_code: zipCode,
        street: data.logradouro,
        neighborhood: data.bairro,
        city: data.localidade,
        state: data.uf,
        registration_origin: 'auto' // Set to 'auto' because it's from ViaCEP API
      };
    } catch (error) {
      console.error('Error fetching address from ViaCEP:', error);
      return null;
    }
  };

  // Import postal codes from CSV
  const importFromCSV = async (csvContent: string): Promise<{ success: number; failed: number }> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        try {
          const lines = csvContent.split('\n');
          // Skip the header row
          const dataRows = lines.slice(1);
          
          let successCount = 0;
          let failedCount = 0;
          const newPostalCodes: PostalCode[] = [...postalCodes];
          const now = new Date().toISOString();
          
          dataRows.forEach(row => {
            if (!row.trim()) return; // Skip empty lines
            
            const columns = row.split(',');
            
            if (columns.length >= 5) {
              const zipCode = columns[0].trim();
              const street = columns[1].trim();
              const neighborhood = columns[2].trim();
              const city = columns[3].trim();
              const state = columns[4].trim();
              // Always set origin to 'manual' for CSV imports
              const origin = 'manual';
              
              // Check if this postal code already exists
              const exists = newPostalCodes.some(pc => 
                pc.zip_code.replace(/[^0-9]/g, '') === zipCode.replace(/[^0-9]/g, '')
              );
              
              if (!exists) {
                // Format the zip code
                const cleanZipCode = zipCode.replace(/[^0-9]/g, '');
                const formattedZipCode = cleanZipCode.length === 8 
                  ? `${cleanZipCode.substring(0, 5)}-${cleanZipCode.substring(5)}`
                  : zipCode;
                
                newPostalCodes.push({
                  id: crypto.randomUUID(),
                  company_id: '1', // Default company ID
                  zip_code: formattedZipCode,
                  street,
                  neighborhood,
                  city,
                  state,
                  registration_origin: origin,
                  created_at: now,
                  updated_at: now,
                });
                
                successCount++;
              } else {
                failedCount++;
              }
            } else {
              failedCount++;
            }
          });
          
          setPostalCodes(newPostalCodes);
          resolve({ success: successCount, failed: failedCount });
        } catch (error) {
          console.error('Error importing CSV:', error);
          resolve({ success: 0, failed: 0 });
        }
      }, 1000);
    });
  };

  return {
    getPostalCodes,
    getPostalCodeById,
    checkPostalCodeExists,
    savePostalCode,
    deletePostalCode,
    searchPostalCodes,
    fetchFromViaCEP,
    importFromCSV
  };
};
