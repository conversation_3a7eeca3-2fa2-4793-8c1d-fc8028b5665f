
import { useState } from "react";
import { Bank } from "@/types/bank";
import { useToast } from "@/hooks/use-toast";

// TODO: Implementar busca de bancos via API
// const { data: banks, loading } = useBanks();

export const useBankManagement = (itemsPerPage: number = 10) => {
  const { toast } = useToast();
  const [banks, setBanks] = useState<Bank[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentBank, setCurrentBank] = useState<Bank | undefined>(undefined);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [bankToDelete, setBankToDelete] = useState<Bank | null>(null);

  // Filtragem e paginação
  const filteredBanks = banks.filter(bank => 
    bank.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    bank.code.includes(searchTerm)
  );

  const paginatedBanks = filteredBanks.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const totalPages = Math.ceil(filteredBanks.length / itemsPerPage);

  // Manipuladores de eventos
  const handleEditBank = (bank: Bank) => {
    setCurrentBank(bank);
    setIsModalOpen(true);
  };

  const handleAddNewBank = () => {
    setCurrentBank(undefined);
    setIsModalOpen(true);
  };

  const handleDeleteBank = (bank: Bank) => {
    setBankToDelete(bank);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (bankToDelete) {
      setBanks(banks.filter(bank => bank.id !== bankToDelete.id));
      toast({
        title: "Banco excluído",
        description: `O banco ${bankToDelete.name} foi removido com sucesso.`,
      });
      setIsDeleteDialogOpen(false);
      setBankToDelete(null);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset para a primeira página ao buscar
  };

  const handleSaveBank = (bankData: Partial<Bank>) => {
    if (currentBank) {
      setBanks(banks.map(bank => 
        bank.id === currentBank.id 
          ? { ...bank, ...bankData }
          : bank
      ));
      toast({
        title: "Banco atualizado",
        description: `As informações do banco ${bankData.name} foram atualizadas com sucesso.`,
      });
    } else {
      const newBank = {
        id: Math.random().toString(36).substring(7),
        code: bankData.code || "",
        name: bankData.name || "",
        logo: bankData.logo
      };
      
      setBanks([...banks, newBank]);
      toast({
        title: "Banco adicionado",
        description: `O banco ${newBank.name} foi adicionado com sucesso.`,
      });
    }
    
    setIsModalOpen(false);
    setCurrentBank(undefined);
  };

  return {
    banks: paginatedBanks,
    searchTerm,
    handleSearchChange,
    currentPage,
    totalPages,
    setCurrentPage,
    isModalOpen,
    setIsModalOpen,
    currentBank,
    handleEditBank,
    handleAddNewBank,
    handleDeleteBank,
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
    bankToDelete,
    confirmDelete,
    handleSaveBank
  };
};
