
import { useState } from 'react';

// Service para gerenciar endereços
// TODO: Implementar chamadas reais para API
export const useAddressService = () => {
  const [addresses, setAddresses] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get all addresses
  const getAddresses = async () => {
    // Simulate API request
    return new Promise<any[]>(resolve => {
      setTimeout(() => {
        resolve(addresses);
      }, 500);
    });
  };

  // Save an address (create or update)
  const saveAddress = async (address: any) => {
    // Simulate API request
    return new Promise<any>(resolve => {
      setTimeout(() => {
        const now = new Date().toISOString();
        
        if (address.id && addresses.some(a => a.id === address.id)) {
          // Update existing address
          const updatedAddresses = addresses.map(a => 
            a.id === address.id ? { ...address, updated_at: now } : a
          );
          setAddresses(updatedAddresses);
        } else {
          // Create new address
          const newAddress = {
            ...address,
            id: address.id || crypto.randomUUID(),
            created_at: now,
            updated_at: now,
          };
          setAddresses([...addresses, newAddress]);
        }
        
        resolve(address);
      }, 500);
    });
  };

  // Delete an address
  const deleteAddress = async (id: string) => {
    // Simulate API request
    return new Promise<void>(resolve => {
      setTimeout(() => {
        setAddresses(addresses.filter(a => a.id !== id));
        resolve();
      }, 500);
    });
  };

  // Search addresses by term
  const searchAddresses = async (term: string) => {
    // Simulate API request
    return new Promise<any[]>(resolve => {
      setTimeout(() => {
        const results = addresses.filter(address => {
          const searchTerm = term.toLowerCase();
          return address.zip_code.toLowerCase().includes(searchTerm) ||
                 address.street.toLowerCase().includes(searchTerm) ||
                 address.city.toLowerCase().includes(searchTerm) ||
                 address.state.toLowerCase().includes(searchTerm) ||
                 address.neighborhood.toLowerCase().includes(searchTerm);
        });
        resolve(results);
      }, 500);
    });
  };

  // Find address by CEP
  const findByCep = async (cep: string) => {
    // Simulate API request
    return new Promise<any>(resolve => {
      setTimeout(() => {
        // Mock CEP data
        const cepData: Record<string, any> = {
          '01310100': {
            street: 'Avenida Paulista',
            neighborhood: 'Bela Vista',
            city: 'São Paulo',
            state: 'SP'
          },
          '22250040': {
            street: 'Rua Voluntários da Pátria',
            neighborhood: 'Botafogo',
            city: 'Rio de Janeiro',
            state: 'RJ'
          },
          '30130170': {
            street: 'Rua dos Carijós',
            neighborhood: 'Centro',
            city: 'Belo Horizonte',
            state: 'MG'
          },
          // Add more mock CEP data as needed
        };
        
        // Clean up the CEP format
        const cleanCep = cep.replace(/[^0-9]/g, '');
        
        if (cepData[cleanCep]) {
          resolve(cepData[cleanCep]);
        } else {
          // Return a random address for any other CEP (for demo purposes only)
          const randomCity = ['São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Porto Alegre', 'Brasília'][Math.floor(Math.random() * 5)];
          const randomState = ['SP', 'RJ', 'MG', 'RS', 'DF'][Math.floor(Math.random() * 5)];
          
          resolve({
            street: 'Rua Exemplo',
            neighborhood: 'Bairro Teste',
            city: randomCity,
            state: randomState
          });
        }
      }, 800);
    });
  };

  return {
    getAddresses,
    saveAddress,
    deleteAddress,
    searchAddresses,
    findByCep
  };
};
