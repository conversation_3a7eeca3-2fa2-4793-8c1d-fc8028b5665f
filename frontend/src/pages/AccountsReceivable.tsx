import { Layout } from "@/components/layout/Layout";
import { useLocation, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { useState } from "react";
import AccountsReceivableTable from "@/components/accounts-receivable/AccountsReceivableTable";
import GlassCard from "@/components/ui-custom/GlassCard";
import OverdueAccountsCard from "@/components/accounts-receivable/OverdueAccountsCard";

// TODO: Implementar hook para buscar contas bancárias via API
// const { data: bankAccounts, loading: accountsLoading } = useBankAccounts();

export default function AccountsReceivable() {
  const location = useLocation();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilter, setActiveFilter] = useState("all");
  const [selectedPeriod, setSelectedPeriod] = useState("current-month");
  const [selectedAccountId, setSelectedAccountId] = useState("all");
  const [selectedEntityId, setSelectedEntityId] = useState("all");
  const [customDateRange, setCustomDateRange] = useState<{
    startDate?: Date;
    endDate?: Date;
  }>({});

  // Estados para dados reais
  const [accounts, setAccounts] = useState<any[]>([]);
  const [accountsReceivable, setAccountsReceivable] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAddNew = () => {
    navigate("/accounts-receivable/new/new");
  };

  const handleEdit = (item: any) => {
    navigate(`/accounts-receivable/${item.id}/edit`);
  };

  const handleView = (item: any) => {
    navigate(`/accounts-receivable/${item.id}/view`);
  };

  const handleFilter = (filter: string) => {
    setActiveFilter(filter);
  };

  const handleClearFilters = () => {
    setSearchTerm("");
  };

  const handleClearAllFilters = () => {
    setSearchTerm("");
    setActiveFilter("all");
    setSelectedPeriod("current-month");
    setSelectedAccountId("all");
    setSelectedEntityId("all");
    setCustomDateRange({});
  };

  const handleDateRangeChange = (startDate?: Date, endDate?: Date) => {
    setCustomDateRange({ startDate, endDate });
    setSelectedPeriod("custom");
  };

  return (
    <Layout location={location}>
      <div className="container p-4 mx-auto">
        <h1 className="text-2xl font-bold mb-6">Contas a Receber</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-5 mb-6">
          <GlassCard className="animate-scale-in" style={{ animationDelay: "0.1s" }}>
            <div className="space-y-1">
              <h3 className="text-sm font-medium text-muted-foreground">Total a Receber</h3>
              <p className="text-3xl font-bold">R$ 8.750,25</p>
            </div>
          </GlassCard>
          
          <OverdueAccountsCard 
            filter={activeFilter}
            selectedPeriod={selectedPeriod}
            selectedEntityId={selectedEntityId}
          />
          
          <GlassCard className="animate-scale-in" style={{ animationDelay: "0.3s" }}>
            <div className="space-y-1">
              <h3 className="text-sm font-medium text-muted-foreground">A Vencer</h3>
              <p className="text-3xl font-bold">R$ 5.499,50</p>
              <p className="text-xs text-muted-foreground">Próximos 30 dias</p>
            </div>
          </GlassCard>
        </div>
        
        <div className="flex mb-6">
          <Button onClick={handleAddNew} className="gap-1 whitespace-nowrap">
            <PlusCircle className="h-4 w-4" />
            Nova Conta
          </Button>
        </div>

        <AccountsReceivableTable 
          onEdit={handleEdit}
          onView={handleView}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          filter={activeFilter}
          onFilterChange={handleFilter}
          selectedPeriod={selectedPeriod}
          onPeriodChange={setSelectedPeriod}
          selectedAccountId={selectedAccountId}
          onAccountChange={setSelectedAccountId}
          selectedEntityId={selectedEntityId}
          onEntityChange={setSelectedEntityId}
          accounts={accounts}
          data={accountsReceivable}
          loading={loading}
          error={error}
          onClearFilters={handleClearFilters}
          onClearAllFilters={handleClearAllFilters}
          customDateRange={customDateRange}
          onDateRangeChange={handleDateRangeChange}
        />
      </div>
    </Layout>
  );
}
