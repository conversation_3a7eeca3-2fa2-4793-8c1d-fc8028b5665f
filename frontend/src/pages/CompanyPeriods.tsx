
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Building2, Calendar, CalendarRange, ChevronLeft, Pencil, Plus, Trash } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { format } from "date-fns";
import { CustomPeriodModal } from "@/components/settings/CustomPeriodModal";
import IconButton from "@/components/ui-custom/IconButton";

interface Period {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  companyId: string;
}

const CompanyPeriods = () => {
  const navigate = useNavigate();
  const { companyId } = useParams();
  const [company, setCompany] = useState<any>(null);
  const [periods, setPeriods] = useState<Period[]>([]);
  const [currentPeriod, setCurrentPeriod] = useState<Period | undefined>(undefined);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [periodToDelete, setPeriodToDelete] = useState<string | null>(null);

  // TODO: Implementar busca de empresas via API
  // const { data: companies, loading: companiesLoading } = useCompanies();

  const mockPeriods = [
    {
      id: '1',
      name: '1º Trimestre 2023',
      startDate: new Date(2023, 0, 1),
      endDate: new Date(2023, 2, 31),
      companyId: '1',
    },
    {
      id: '2',
      name: '2º Trimestre 2023',
      startDate: new Date(2023, 3, 1),
      endDate: new Date(2023, 5, 30),
      companyId: '1',
    },
    {
      id: '3',
      name: '1º Semestre 2023',
      startDate: new Date(2023, 0, 1),
      endDate: new Date(2023, 5, 30),
      companyId: '2',
    },
    {
      id: '4',
      name: 'Exercício 2023',
      startDate: new Date(2023, 0, 1),
      endDate: new Date(2023, 11, 31),
      companyId: '4',
    },
  ];

  // Load company and periods data
  useEffect(() => {
    if (companyId) {
      // TODO: Implementar busca de empresa e períodos via API
      // const loadCompanyData = async () => {
      //   try {
      //     const companyData = await fetchCompany(companyId);
      //     setCompany(companyData);
      //     const periodsData = await fetchCompanyPeriods(companyId);
      //     setPeriods(periodsData);
      //   } catch (error) {
      //     console.error('Erro ao carregar dados da empresa:', error);
      //   }
      // };
      // loadCompanyData();

      // Temporariamente, mostrar estado vazio até implementar API
      setCompany(null);
      setPeriods([]);
    }
  }, [companyId]);

  const handleAddPeriod = () => {
    setCurrentPeriod(undefined);
    setIsModalOpen(true);
  };

  const handleEditPeriod = (period: Period) => {
    setCurrentPeriod(period);
    setIsModalOpen(true);
  };

  const handleDeletePeriod = (periodId: string) => {
    setPeriodToDelete(periodId);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = () => {
    if (periodToDelete) {
      setPeriods(periods.filter(period => period.id !== periodToDelete));
      setPeriodToDelete(null);
      setDeleteConfirmOpen(false);
    }
  };

  const handleSavePeriod = (periodData: any) => {
    if (currentPeriod) {
      // Update existing period
      setPeriods(periods.map(period => 
        period.id === currentPeriod.id 
          ? { 
              ...period, 
              ...periodData,
            } 
          : period
      ));
    } else {
      // Add new period
      const newPeriod: Period = {
        ...periodData,
        id: Math.random().toString(36).substring(7),
        companyId: companyId!,
      };
      setPeriods([...periods, newPeriod]);
    }
    setIsModalOpen(false);
    setCurrentPeriod(undefined);
  };

  if (!company) {
    return <div>Carregando...</div>;
  }

  return (
    <Layout>
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <CalendarRange className="h-8 w-8 text-primary mr-3" />
            <div>
              <h1 className="text-3xl font-bold">Períodos Personalizados</h1>
              <p className="text-lg text-muted-foreground">
                Empresa: <span className="font-medium text-foreground">{company.name}</span>
              </p>
            </div>
          </div>
          <Button 
            variant="outline" 
            onClick={() => navigate("/settings/companies")}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="h-4 w-4" />
            Voltar para Empresas
          </Button>
        </div>
        
        <div className="grid gap-6">
          <div className="bg-white dark:bg-dark-card rounded-lg border dark:border-dark-border shadow p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Cadastrar Novo Período
              </h2>
              <Button
                onClick={handleAddPeriod}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Novo Período
              </Button>
            </div>
          </div>
          
          <div className="bg-white dark:bg-dark-card rounded-lg border dark:border-dark-border shadow">
            <div className="p-6 border-b dark:border-dark-border">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Períodos Cadastrados
              </h2>
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome do Período</TableHead>
                  <TableHead>Data Inicial</TableHead>
                  <TableHead>Data Final</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {periods.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-6 text-muted-foreground">
                      Nenhum período cadastrado para esta empresa.
                    </TableCell>
                  </TableRow>
                ) : (
                  periods.map((period) => (
                    <TableRow key={period.id}>
                      <TableCell className="font-medium">{period.name}</TableCell>
                      <TableCell>{format(period.startDate, "dd/MM/yyyy")}</TableCell>
                      <TableCell>{format(period.endDate, "dd/MM/yyyy")}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <IconButton 
                            icon={<Pencil className="h-4 w-4" />}
                            variant="ghost"
                            size="sm"
                            label="Editar"
                            onClick={() => handleEditPeriod(period)}
                          />
                          <IconButton 
                            icon={<Trash className="h-4 w-4" />}
                            variant="ghost"
                            size="sm"
                            label="Excluir"
                            onClick={() => handleDeletePeriod(period.id)}
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
      
      <CustomPeriodModal 
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        onSave={handleSavePeriod}
        period={currentPeriod}
      />
      
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este período? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default CompanyPeriods;
