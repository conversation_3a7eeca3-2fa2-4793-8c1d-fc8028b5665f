import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, Users, HelpCircle, ArrowRight } from 'lucide-react';
import { toast } from 'sonner';
import CreateCompanyForm from '@/components/onboarding/CreateCompanyForm';
import JoinCompanyForm from '@/components/onboarding/JoinCompanyForm';

type OnboardingStep = 'welcome' | 'create-company' | 'join-company' | 'help';

const Onboarding = () => {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('welcome');
  const { user, isAuthenticated, checkAuthStatus } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Verificar se o usuário está autenticado
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    // Verificar se o usuário já tem empresa associada
    if (user?.status !== 'no_company') {
      navigate('/');
      return;
    }
  }, [isAuthenticated, user, navigate]);

  const handleStepChange = (step: OnboardingStep) => {
    setCurrentStep(step);
  };

  const handleCompanyCreated = async () => {
    toast.success('Empresa criada com sucesso!');
    // Atualizar status do usuário
    await checkAuthStatus();
    navigate('/');
  };

  const handleCompanyJoined = async () => {
    toast.success('Associado à empresa com sucesso!');
    // Atualizar status do usuário
    await checkAuthStatus();
    navigate('/');
  };

  if (!isAuthenticated || user?.status !== 'no_company') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {currentStep === 'welcome' && (
          <Card className="mx-auto max-w-2xl">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-bold text-gray-900">
                Bem-vindo ao FluxoMax!
              </CardTitle>
              <CardDescription className="text-lg text-gray-600">
                Para começar a usar o sistema, você precisa estar associado a uma empresa.
                Escolha uma das opções abaixo:
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card 
                  className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-blue-500"
                  onClick={() => handleStepChange('create-company')}
                >
                  <CardContent className="p-6 text-center">
                    <Building2 className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">Criar Nova Empresa</h3>
                    <p className="text-gray-600 mb-4">
                      Estou começando um novo negócio e quero criar minha empresa no sistema
                    </p>
                    <Button className="w-full">
                      Criar Empresa
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>

                <Card 
                  className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-green-500"
                  onClick={() => handleStepChange('join-company')}
                >
                  <CardContent className="p-6 text-center">
                    <Users className="h-12 w-12 text-green-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">Associar-se a Empresa</h3>
                    <p className="text-gray-600 mb-4">
                      Trabalho para uma empresa existente e tenho um código de acesso ou convite
                    </p>
                    <Button variant="outline" className="w-full">
                      Associar-se
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              </div>

              <Card 
                className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-yellow-500"
                onClick={() => handleStepChange('help')}
              >
                <CardContent className="p-4 text-center">
                  <HelpCircle className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold mb-1">Não tenho certeza</h3>
                  <p className="text-gray-600 text-sm">
                    Preciso de ajuda para decidir qual opção escolher
                  </p>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        )}

        {currentStep === 'create-company' && (
          <CreateCompanyForm 
            onSuccess={handleCompanyCreated}
            onBack={() => handleStepChange('welcome')}
          />
        )}

        {currentStep === 'join-company' && (
          <JoinCompanyForm 
            onSuccess={handleCompanyJoined}
            onBack={() => handleStepChange('welcome')}
          />
        )}

        {currentStep === 'help' && (
          <Card className="mx-auto max-w-2xl">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-center">
                Como escolher a opção certa?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h3 className="font-semibold text-blue-700">Criar Nova Empresa</h3>
                  <p className="text-gray-600">
                    Escolha esta opção se você é o proprietário ou responsável por uma empresa
                    e quer começar a usar o FluxoMax para gerenciar as finanças do seu negócio.
                  </p>
                </div>

                <div className="border-l-4 border-green-500 pl-4">
                  <h3 className="font-semibold text-green-700">Associar-se a Empresa</h3>
                  <p className="text-gray-600">
                    Escolha esta opção se você é funcionário de uma empresa que já usa o FluxoMax
                    e recebeu um convite por email ou um código de acesso do seu supervisor.
                  </p>
                </div>

                <div className="border-l-4 border-yellow-500 pl-4">
                  <h3 className="font-semibold text-yellow-700">Ainda com dúvidas?</h3>
                  <p className="text-gray-600">
                    Entre em contato com o administrador da sua empresa ou com o suporte
                    do FluxoMax para obter mais informações.
                  </p>
                </div>
              </div>

              <div className="flex gap-4 justify-center">
                <Button 
                  variant="outline" 
                  onClick={() => handleStepChange('welcome')}
                >
                  Voltar
                </Button>
                <Button 
                  onClick={() => handleStepChange('create-company')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Criar Empresa
                </Button>
                <Button 
                  onClick={() => handleStepChange('join-company')}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Associar-se
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default Onboarding;
