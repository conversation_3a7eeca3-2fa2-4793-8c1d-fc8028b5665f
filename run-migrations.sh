#!/bin/bash

echo "🚀 Executando migrations para sistema de convites e códigos de empresa..."

# Verificar se o Docker está rodando
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker não está rodando. Por favor, inicie o Docker primeiro."
    exit 1
fi

# Verificar se o arquivo docker-compose.dev.yaml existe
if [ ! -f "docker-compose.dev.yaml" ]; then
    echo "❌ Arquivo docker-compose.dev.yaml não encontrado."
    echo "Certifique-se de estar no diretório raiz do projeto."
    exit 1
fi

# Verificar se os containers estão rodando
echo "🔍 Verificando status dos containers..."
if ! docker compose -f docker-compose.dev.yaml ps | grep -q "Up"; then
    echo "⚠️  Containers não estão rodando. Use './dev.sh start' para iniciar o ambiente."
    echo "Ou execute: docker compose -f docker-compose.dev.yaml up -d"
    exit 1
fi

# Aguardar o banco estar pronto
echo "⏳ Aguardando banco de dados estar pronto..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma db push" > /dev/null 2>&1; then
        echo "✅ Banco de dados conectado!"
        break
    else
        echo "⏳ Tentativa $attempt/$max_attempts - Aguardando banco..."
        sleep 2
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ Não foi possível conectar ao banco de dados após $max_attempts tentativas."
    echo "Verifique os logs: ./dev.sh logs:db"
    exit 1
fi

# Executar migrations no container do backend
echo "🔄 Executando migrations do Prisma no container..."
docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma migrate dev --name 'add_invitations_and_codes_system'"

if [ $? -ne 0 ]; then
    echo "❌ Erro ao executar migrations. Tentando com migrate deploy..."
    docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma migrate deploy"
fi

# Gerar cliente Prisma
echo "🔄 Gerando cliente Prisma no container..."
docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma generate"

# Reiniciar o container do backend para aplicar as mudanças
echo "🔄 Reiniciando container do backend..."
docker compose -f docker-compose.dev.yaml restart backend

echo "✅ Migrations executadas com sucesso!"

# Verificar se os containers estão saudáveis
echo "🏥 Verificando saúde dos containers..."
sleep 5
docker compose -f docker-compose.dev.yaml ps

echo ""
echo "🎉 Sistema de convites e códigos de empresa configurado!"
echo ""
echo "📋 Próximos passos:"
echo "1. Verifique se os containers estão rodando: ./dev.sh logs"
echo "2. Acesse o backend em: http://localhost:3000"
echo "3. Acesse o frontend em: http://localhost:3001"
echo "4. Teste o sistema de onboarding registrando um novo usuário"
echo ""
echo "🔧 Comandos úteis do dev.sh:"
echo "- Ver logs do backend: ./dev.sh logs:backend"
echo "- Ver logs do frontend: ./dev.sh logs:frontend"
echo "- Ver logs do banco: ./dev.sh logs:db"
echo "- Parar containers: ./dev.sh stop"
echo "- Reiniciar containers: ./dev.sh restart"
