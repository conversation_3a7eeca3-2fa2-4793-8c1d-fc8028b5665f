#!/bin/bash

echo "🚀 Executando migrations para sistema de convites e códigos de empresa..."

# Verificar se o Docker está rodando
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker não está rodando. Por favor, inicie o Docker primeiro."
    exit 1
fi

# Verificar se os containers estão rodando
echo "🔍 Verificando status dos containers..."
if ! docker-compose ps | grep -q "Up"; then
    echo "⚠️  Containers não estão rodando. Iniciando containers..."
    docker-compose up -d
    echo "⏳ Aguardando containers iniciarem..."
    sleep 10
fi

# Executar migrations no container do backend
echo "🔄 Executando migrations do Prisma no container..."
docker-compose exec backend npx prisma migrate dev --name "add_invitations_and_codes_system"

if [ $? -ne 0 ]; then
    echo "❌ Erro ao executar migrations. Tentando com migrate deploy..."
    docker-compose exec backend npx prisma migrate deploy
fi

# Gerar cliente Prisma
echo "🔄 Gerando cliente Prisma no container..."
docker-compose exec backend npx prisma generate

# Reiniciar o container do backend para aplicar as mudanças
echo "🔄 Reiniciando container do backend..."
docker-compose restart backend

echo "✅ Migrations executadas com sucesso!"

# Verificar se os containers estão saudáveis
echo "🏥 Verificando saúde dos containers..."
sleep 5
docker-compose ps

echo ""
echo "🎉 Sistema de convites e códigos de empresa configurado!"
echo ""
echo "📋 Próximos passos:"
echo "1. Verifique se os containers estão rodando: docker-compose ps"
echo "2. Acesse o backend em: http://localhost:3001"
echo "3. Acesse o frontend em: http://localhost:3000"
echo "4. Teste o sistema de onboarding registrando um novo usuário"
echo ""
echo "🔧 Comandos úteis:"
echo "- Ver logs do backend: docker-compose logs -f backend"
echo "- Ver logs do frontend: docker-compose logs -f frontend"
echo "- Parar containers: docker-compose down"
echo "- Reiniciar containers: docker-compose restart"
