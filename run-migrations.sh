#!/bin/bash

echo "🚀 Executando migrations para sistema de convites e códigos de empresa..."

# Navegar para o diretório do backend
cd backend

echo "📦 Instalando dependências..."
npm install

echo "🔄 Executando migrations do Prisma..."
npx prisma migrate dev --name "add_invitations_and_codes_system"

echo "🔄 Gerando cliente Prisma..."
npx prisma generate

echo "✅ Migrations executadas com sucesso!"

# Voltar para o diretório raiz
cd ..

echo "🎉 Sistema de convites e códigos de empresa configurado!"
echo ""
echo "📋 Próximos passos:"
echo "1. Inicie o backend: cd backend && npm run dev"
echo "2. Inicie o frontend: cd frontend && npm run dev"
echo "3. Acesse http://localhost:3000 para testar o sistema"
