-- Adicionar novo valor ao enum de status do usuário
-- <PERSON><PERSON>, criar um novo tipo temporário
CREATE TYPE "UserStatus_new" AS ENUM ('active', 'inactive', 'pending', 'without_company');

-- Alterar a coluna para usar o novo tipo
ALTER TABLE "users" ALTER COLUMN "status" TYPE "UserStatus_new" USING ("status"::text::"UserStatus_new");

-- Remover o tipo antigo
DROP TYPE "UserStatus";

-- Renomear o novo tipo
ALTER TYPE "UserStatus_new" RENAME TO "UserStatus";
