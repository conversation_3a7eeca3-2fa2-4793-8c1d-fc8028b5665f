// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String    @id @default(uuid()) @db.Uuid
  email     String    @unique @db.VarChar(255)
  password  String    @db.VarChar(255)
  status    String    @default("active") @db.VarChar(20) // active, inactive, pending, no_company
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  profile          Profile?
  refreshTokens    RefreshToken[]
  userCompanyRoles UserCompanyRole[]
  invitationsSent  CompanyInvitation[] @relation("InvitationsSent")
  companyCodesCreated CompanyCode[] @relation("CompanyCodesCreated")

  @@map("users")
}

model RefreshToken {
  id        String   @id @default(uuid()) @db.Uuid
  token     String   @unique
  userId    String   @map("user_id") @db.Uuid
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")

  @@map("refresh_tokens")
}

model Profile {
  id          String    @id @db.Uuid
  username    String    @unique @db.VarChar(255)
  firstName   String?   @map("first_name") @db.VarChar(100)
  lastName    String?   @map("last_name") @db.VarChar(100)
  phone       String?   @db.VarChar(20)
  avatarUrl   String?   @map("avatar_url") @db.VarChar(255)
  preferences Json?     @db.JsonB
  isActive    Boolean   @default(true) @map("is_active")
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt   DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  user User @relation(fields: [id], references: [id], onDelete: Cascade)

  @@map("profiles")
}

model UserCompanyRole {
  id        String    @id @default(uuid()) @db.Uuid
  userId    String    @map("user_id") @db.Uuid
  companyId String    @map("company_id") @db.Uuid
  roleId    String    @map("role_id") @db.Uuid
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  company Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  role    Role    @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, companyId, roleId])
  @@map("user_company_roles")
}

model SystemPermission {
  id          String       @id @default(uuid()) @db.Uuid
  code        String       @unique @db.VarChar(100)
  name        String       @db.VarChar(100)
  description String?      @db.VarChar(255)
  module      String       @db.VarChar(50)
  createdAt   DateTime     @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime     @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  permissions Permission[]

  @@map("system_permissions")
}

model Permission {
  id                 String            @id @default(uuid()) @db.Uuid
  companyId          String            @map("company_id") @db.Uuid
  action             String            @db.VarChar(100)
  description        String?           @db.VarChar(255)
  systemPermissionId String?           @map("system_permission_id") @db.Uuid
  createdAt          DateTime          @default(now()) @map("created_at") @db.Timestamptz
  updatedAt          DateTime          @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  company           Company            @relation(fields: [companyId], references: [id], onDelete: Cascade)
  systemPermission  SystemPermission?  @relation(fields: [systemPermissionId], references: [id], onDelete: Cascade)
  rolePermissions   RolePermission[]

  @@map("permissions")
}

model Role {
  id          String    @id @default(uuid()) @db.Uuid
  companyId   String    @map("company_id") @db.Uuid
  name        String    @db.VarChar(255)
  description String?   @db.Text
  isAdmin     Boolean   @default(false) @map("is_admin") @db.Boolean
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt   DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  company         Company          @relation(fields: [companyId], references: [id], onDelete: Cascade)
  userCompanyRoles UserCompanyRole[]
  rolePermissions  RolePermission[]
  invitations     CompanyInvitation[]
  companyCodes    CompanyCode[]

  @@unique([companyId, name])
  @@map("roles")
}

model RolePermission {
  roleId       String     @map("role_id") @db.Uuid
  permissionId String     @map("permission_id") @db.Uuid

  // Relations
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@id([roleId, permissionId])
  @@map("role_permissions")
}

model Company {
  id            String    @id @default(uuid()) @db.Uuid
  name          String    @db.VarChar(255)
  cnpj          String    @unique @db.VarChar(18)
  phone         String?   @db.VarChar(20)
  email         String?   @db.VarChar(255)
  address_id    String?   @unique @db.Uuid
  logo          String?   @db.VarChar(255)
  active        Boolean?  @default(true)
  calendar_type String?   @default("standard") // Assuming TEXT maps to String, check DB constraints
  created_at    DateTime? @default(now()) @db.Timestamptz
  updated_at    DateTime? @updatedAt @db.Timestamptz
  deleted_at    DateTime? @db.Timestamptz

  // Relations
  mainAddress       Address?          @relation("CompanyAddress", fields: [address_id], references: [id])
  accountsPayable   AccountsPayable[]
  accountsReceivable AccountsReceivable[]
  entities          Entity[]
  categories        Category[]
  projects          Project[]
  // paymentMethods  PaymentMethod[] // Removed relation as PaymentMethod is now global
  bankAccounts      BankAccount[]
  addresses         Address[]         @relation("CompanyAddresses")
  // recurrenceTypes RecurrenceType[] // Removed relation as RecurrenceType is now global
  transactions      Transaction[]
  recurringSchedules RecurringSchedule[]
  // banks            Bank[] // REMOVED - Banks are global

  userCompanyRoles UserCompanyRole[]
  roles            Role[]
  permissions      Permission[]
  customPeriods    CustomPeriod[]
  invitations      CompanyInvitation[]
  companyCodes     CompanyCode[]

  @@map("companies")
}

model AddressType {
  id          String    @id @default(uuid()) @db.Uuid
  name        String    @unique @db.VarChar(50)
  description String?   @db.VarChar(255)

  // Relations
  addresses   Address[]

  @@map("address_types")
}

model Address {
  id            String    @id @default(uuid()) @db.Uuid
  companyId     String?   @map("company_id") @db.Uuid
  entityId      String?   @map("entity_id") @db.Uuid
  addressTypeId String?   @map("address_type_id") @db.Uuid
  street        String    @db.VarChar(255)
  number        String?   @db.VarChar(20)
  complement    String?   @db.VarChar(255)
  district      String    @db.VarChar(100)
  city          String    @db.VarChar(100)
  state         String    @db.VarChar(2)
  zipCode       String    @map("zip_code") @db.VarChar(10)
  isDefault     Boolean   @default(false) @map("is_default") @db.Boolean
  createdAt     DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt     DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt     DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  company      Company?     @relation("CompanyAddresses", fields: [companyId], references: [id], onDelete: Cascade)
  entity       Entity?      @relation("EntityAddresses", fields: [entityId], references: [id], onDelete: Cascade)
  addressType  AddressType? @relation(fields: [addressTypeId], references: [id])
  companyWithMainAddress Company? @relation("CompanyAddress")

  @@map("addresses")
}

// Add Bank model (based on docs/database.sql)
model Bank {
  id        String    @id @default(uuid()) @db.Uuid
  code      String    @unique @db.VarChar(10)
  name      String    @db.VarChar(100)
  logo      String?   @db.VarChar(255)
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  // deletedAt DateTime? @map("deleted_at") @db.Timestamptz // Not in docs/database.sql schema
  // companyId String    @map("company_id") @db.Uuid // REMOVED - Banks are global

  // Relations
  // company      Company       @relation(fields: [companyId], references: [id], onDelete: Cascade) // REMOVED - Banks are global
  bankAccounts BankAccount[]

  @@map("banks")
}

// Add ZipCode model (based on docs/database.sql)
model ZipCode {
  zipCode            String    @id @map("zip_code") @db.VarChar(9)
  street             String    @db.VarChar(255)
  neighborhood       String    @db.VarChar(100)
  city               String    @db.VarChar(100)
  state              String    @db.VarChar(2)
  registrationOrigin String    @default("auto") @map("registration_origin") @db.VarChar(10) // manual, auto
  expiresAt          DateTime? @map("expires_at") @db.Timestamptz // Campo para TTL do cache
  createdAt          DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt          DateTime  @updatedAt @map("updated_at") @db.Timestamptz

  @@map("zip_codes")
}

model AccountsPayable {
  id                String     @id @default(uuid()) @db.Uuid
  companyId         String     @map("company_id") @db.Uuid
  description       String     @db.VarChar(255)
  entityId          String     @map("entity_id") @db.Uuid
  dueDate           DateTime   @map("due_date") @db.Date // Use Date if only date is needed, Timestamptz if time is relevant
  amount            Decimal    @db.Decimal(10, 2) // Adjust precision/scale as needed
  paidAmount        Decimal    @default(0) @map("paid_amount") @db.Decimal(10, 2)
  currencyId        String     @map("currency_id") @db.Uuid
  status            String     @db.VarChar(50) // e.g., pending, paid, partially_paid, overdue, cancelled
  categoryId        String?    @map("category_id") @db.Uuid
  projectId         String?    @map("project_id") @db.Uuid
  paymentMethodId   String?    @map("payment_method_id") @db.Uuid
  bankAccountId     String?    @map("bank_account_id") @db.Uuid // Changed to optional based on docs FK (ON DELETE SET NULL)
  recurrenceTypeId  String?    @map("recurrence_type_id") @db.Uuid
  invoiceNumber     String?    @map("invoice_number") @db.VarChar(100)
  notes             String?    @db.Text
  interestAmount    Decimal?   @default(0) @map("interest_amount") @db.Decimal(10, 2)
  discountAmount    Decimal?   @default(0) @map("discount_amount") @db.Decimal(10, 2)
  // finalValue is calculated, not stored directly in this model via Prisma schema
  installments      Int?
  installmentNumber Int?       @map("installment_number")
  parentId          String?    @map("parent_id") @db.Uuid
  createdAt         DateTime   @default(now()) @map("created_at") @db.Timestamptz
  updatedAt         DateTime   @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt         DateTime?  @map("deleted_at") @db.Timestamptz

  // Relations
  company         Company          @relation(fields: [companyId], references: [id], onDelete: Cascade)
  entity          Entity           @relation(fields: [entityId], references: [id])
  currency        Currency         @relation(fields: [currencyId], references: [id])
  category        Category?        @relation(fields: [categoryId], references: [id])
  project         Project?         @relation(fields: [projectId], references: [id])
  paymentMethod   PaymentMethod?   @relation(fields: [paymentMethodId], references: [id])
  bankAccount     BankAccount?     @relation(fields: [bankAccountId], references: [id], onDelete: SetNull) // Changed onDelete based on docs FK
  recurrenceType  RecurrenceType?  @relation(fields: [recurrenceTypeId], references: [id])
  parent          AccountsPayable? @relation("Installments", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction) // Self-relation for parent
  children        AccountsPayable[] @relation("Installments") // Self-relation for children installments
  transactions    Transaction[]

  @@map("accounts_payable")
}

model AccountsReceivable {
  id                String     @id @default(uuid()) @db.Uuid
  companyId         String     @map("company_id") @db.Uuid
  description       String     @db.VarChar(255)
  entityId          String     @map("entity_id") @db.Uuid
  dueDate           DateTime   @map("due_date") @db.Date
  amount            Decimal    @db.Decimal(10, 2)
  receivedAmount    Decimal    @default(0) @map("received_amount") @db.Decimal(10, 2)
  currencyId        String     @map("currency_id") @db.Uuid
  status            String     @db.VarChar(50) // pending, received, partially_received, overdue, cancelled
  categoryId        String?    @map("category_id") @db.Uuid
  projectId         String?    @map("project_id") @db.Uuid
  paymentMethodId   String?    @map("payment_method_id") @db.Uuid
  bankAccountId     String?    @map("bank_account_id") @db.Uuid
  recurrenceTypeId  String?    @map("recurrence_type_id") @db.Uuid
  invoiceNumber     String?    @map("invoice_number") @db.VarChar(100)
  notes             String?    @db.Text
  interestAmount    Decimal?   @default(0) @map("interest_amount") @db.Decimal(10, 2)
  discountAmount    Decimal?   @default(0) @map("discount_amount") @db.Decimal(10, 2)
  installments      Int?
  installmentNumber Int?       @map("installment_number")
  parentId          String?    @map("parent_id") @db.Uuid
  createdAt         DateTime   @default(now()) @map("created_at") @db.Timestamptz
  updatedAt         DateTime   @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt         DateTime?  @map("deleted_at") @db.Timestamptz

  // Relations
  company         Company            @relation(fields: [companyId], references: [id], onDelete: Cascade)
  entity          Entity             @relation(fields: [entityId], references: [id])
  currency        Currency           @relation(fields: [currencyId], references: [id])
  category        Category?          @relation(fields: [categoryId], references: [id])
  project         Project?           @relation(fields: [projectId], references: [id])
  paymentMethod   PaymentMethod?     @relation(fields: [paymentMethodId], references: [id])
  bankAccount     BankAccount?       @relation(fields: [bankAccountId], references: [id], onDelete: SetNull)
  recurrenceType  RecurrenceType?    @relation(fields: [recurrenceTypeId], references: [id])
  parent          AccountsReceivable? @relation("Installments", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  children        AccountsReceivable[] @relation("Installments")
  transactions    Transaction[]

  @@map("accounts_receivable")
}

model Entity {
  id        String    @id @default(uuid()) @db.Uuid
  companyId String    @map("company_id") @db.Uuid
  type      String    @db.VarChar(50) // customer, supplier
  name      String    @db.VarChar(255)
  cnpj      String?   @db.VarChar(18)
  phone     String?   @db.VarChar(20)
  contact   String?   @db.VarChar(100)
  email     String?   @db.VarChar(255)
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  company            Company           @relation(fields: [companyId], references: [id], onDelete: Cascade)
  accountsPayable    AccountsPayable[]
  accountsReceivable AccountsReceivable[]
  transactions       Transaction[]
  addresses          Address[]         @relation("EntityAddresses")
  recurringSchedules RecurringSchedule[]

  @@map("entities")
}

model Currency {
  id     String @id @default(uuid()) @db.Uuid
  code   String @unique @db.VarChar(3) // e.g., BRL, USD
  name   String @db.VarChar(50)
  symbol String @db.VarChar(5)
  decimalPlaces Int     @default(2) @map("decimal_places") // Added based on docs/database.sql
  isDefault     Boolean @default(false) @map("is_default") // Added based on docs/database.sql

  accountsPayable    AccountsPayable[]
  accountsReceivable AccountsReceivable[]
  bankAccounts       BankAccount[]
  recurringSchedules RecurringSchedule[]
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz // Added timestamp
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz // Added timestamp

  @@map("currencies")
}

model Category {
  id        String    @id @default(uuid()) @db.Uuid
  companyId String    @map("company_id") @db.Uuid
  name      String    @db.VarChar(100)
  transactionType String @map("transaction_type") @db.VarChar(50) // Renamed from 'type', mapped to db column
  parentId  String?   @map("parent_id") @db.Uuid
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  company            Company           @relation(fields: [companyId], references: [id], onDelete: Cascade)
  parent             Category?         @relation("SubCategories", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  children           Category[]        @relation("SubCategories")
  accountsPayable    AccountsPayable[]
  accountsReceivable AccountsReceivable[]
  transactions       Transaction[]
  recurringSchedules RecurringSchedule[]

  @@map("categories")
}

model Project {
  id          String    @id @default(uuid()) @db.Uuid
  companyId   String    @map("company_id") @db.Uuid
  name        String    @db.VarChar(150)
  description String?   @db.Text
  budget      Decimal?  @db.Decimal(12, 2)
  startDate   DateTime? @map("start_date") @db.Date
  endDate     DateTime? @map("end_date") @db.Date
  status      String    @db.VarChar(50) // e.g., planned, in_progress, completed, on_hold, canceled
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt   DateTime? @map("deleted_at") @db.Timestamptz

  company            Company           @relation(fields: [companyId], references: [id], onDelete: Cascade)
  accountsPayable    AccountsPayable[]
  accountsReceivable AccountsReceivable[]
  transactions       Transaction[]
  recurringSchedules RecurringSchedule[]

  @@map("projects")
}

model PaymentMethod {
  id        String    @id @default(uuid()) @db.Uuid
  name      String    @unique @db.VarChar(50) // Made unique based on seed script ON CONFLICT(name)
  description String? @db.VarChar(255) // Added description
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  // Removed companyId, isEnabled, deletedAt to match global lookup nature from docs/seed

  // Relations (Now global, so no direct company link)
  accountsPayable    AccountsPayable[]
  accountsReceivable AccountsReceivable[]
  transactions       Transaction[]
  recurringSchedules RecurringSchedule[]

  @@map("payment_methods")
}

// Update BankAccount model
model BankAccount {
  id             String    @id @default(uuid()) @db.Uuid
  companyId      String    @map("company_id") @db.Uuid
  bankId         String    @map("bank_id") @db.Uuid // Relation to Bank model
  accountNumber  String    @map("account_number") @db.VarChar(20) // Adjusted size based on docs
  accountType    String    @map("account_type") @db.VarChar(50) // checking, savings, investment, cash, other
  
  // Campos de saldo reformulados
  initialBalance Decimal   @default(0) @map("initial_balance") @db.Decimal(12, 2)
  currentBalance Decimal   @default(0) @map("current_balance") @db.Decimal(12, 2)
  isInitialBalanceLocked Boolean @default(false) @map("is_initial_balance_locked")
  
  balanceDate    DateTime  @map("balance_date") @db.Timestamptz // Added from docs
  creditLimit    Decimal?  @default(0) @map("credit_limit") @db.Decimal(12, 2) // Added from docs, made optional
  firstTransactionAt DateTime? @map("first_transaction_at") @db.Timestamptz
  currencyId     String    @map("currency_id") @db.Uuid // Keep relation
  name           String    @db.VarChar(100) // Added name field (not in docs schema but useful)
  isEnabled      Boolean   @default(true) @map("is_enabled") // Keep isEnabled
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt      DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt      DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  company              Company           @relation(fields: [companyId], references: [id], onDelete: Cascade)
  bank                 Bank              @relation(fields: [bankId], references: [id], onDelete: Restrict) // Changed onDelete based on docs FK
  currency             Currency          @relation(fields: [currencyId], references: [id]) // Keep relation
  accountsPayable      AccountsPayable[]
  accountsReceivable   AccountsReceivable[]
  transactions         Transaction[] @relation("SourceAccount")
  destinationTransactions Transaction[] @relation("DestinationAccount")
  recurringSchedules   RecurringSchedule[]

  @@map("bank_accounts")
}

model RecurrenceType {
  id        String    @id @default(uuid()) @db.Uuid
  name      String    @unique @db.VarChar(50) // Made unique based on seed script ON CONFLICT(name)
  description String? @db.VarChar(255) // Added description
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  // Removed companyId, interval, deletedAt to match global lookup nature from docs/seed

  // Relations (Now global)
  accountsPayable    AccountsPayable[]
  accountsReceivable AccountsReceivable[]
  recurringSchedules RecurringSchedule[]

  @@map("recurrence_types")
}

model CustomPeriod {
  id        String    @id @default(uuid()) @db.Uuid
  name      String    @db.VarChar(100)
  startDate DateTime  @map("start_date") @db.Date
  endDate   DateTime  @map("end_date") @db.Date
  companyId String    @map("company_id") @db.Uuid
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  company   Company   @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@map("custom_periods")
}

model Transaction {
  id                String    @id @default(uuid()) @db.Uuid
  companyId         String    @map("company_id") @db.Uuid
  type              String    @db.VarChar(50) // expense, income, transfer
  amount            Decimal   @db.Decimal(12, 2)
  description       String    @db.VarChar(255)
  transactionDate   DateTime  @map("transaction_date") @db.Timestamptz
  bankAccountId     String    @map("bank_account_id") @db.Uuid
  destinationAccountId String? @map("destination_account_id") @db.Uuid // For transfers
  categoryId        String?   @map("category_id") @db.Uuid
  entityId          String?   @map("entity_id") @db.Uuid
  projectId         String?   @map("project_id") @db.Uuid
  paymentMethodId   String?   @map("payment_method_id") @db.Uuid
  accountsPayableId String?   @map("accounts_payable_id") @db.Uuid // Reference to accounts_payable if this is a payment
  accountsReceivableId String? @map("accounts_receivable_id") @db.Uuid // Reference to accounts_receivable if this is a receipt
  notes             String?   @db.Text
  createdAt         DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt         DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt         DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  company           Company     @relation(fields: [companyId], references: [id], onDelete: Cascade)
  bankAccount       BankAccount @relation("SourceAccount", fields: [bankAccountId], references: [id], onDelete: Restrict)
  destinationAccount BankAccount? @relation("DestinationAccount", fields: [destinationAccountId], references: [id], onDelete: Restrict)
  category          Category?   @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  entity            Entity?     @relation(fields: [entityId], references: [id], onDelete: SetNull)
  project           Project?    @relation(fields: [projectId], references: [id], onDelete: SetNull)
  paymentMethod     PaymentMethod? @relation(fields: [paymentMethodId], references: [id], onDelete: SetNull)
  accountsPayable   AccountsPayable? @relation(fields: [accountsPayableId], references: [id], onDelete: SetNull)
  accountsReceivable AccountsReceivable? @relation(fields: [accountsReceivableId], references: [id], onDelete: SetNull)

  @@map("transactions")
}

model RecurringSchedule {
  id                 String    @id @default(uuid()) @db.Uuid
  companyId          String    @map("company_id") @db.Uuid
  recurrenceTypeId   String    @map("recurrence_type_id") @db.Uuid
  entityId           String?   @map("entity_id") @db.Uuid
  description        String    @db.VarChar(255)
  referenceTable     String    @map("reference_table") @db.VarChar(50) // accounts_payable, accounts_receivable
  referenceId        String?   @map("reference_id") @db.Uuid
  dayOfMonth         Int?      @map("day_of_month") // 1-31
  dayOfWeek          Int?      @map("day_of_week") // 0-6 (0 = Sunday, 6 = Saturday)
  startDate          DateTime  @map("start_date") @db.Timestamptz
  endDate            DateTime? @map("end_date") @db.Timestamptz
  amount             Decimal   @db.Decimal(10, 2)
  currencyId         String    @map("currency_id") @db.Uuid
  categoryId         String?   @map("category_id") @db.Uuid
  projectId          String?   @map("project_id") @db.Uuid
  paymentMethodId    String?   @map("payment_method_id") @db.Uuid
  bankAccountId      String?   @map("bank_account_id") @db.Uuid
  nextGenerationDate DateTime? @map("next_generation_date") @db.Timestamptz
  active             Boolean   @default(true)
  createdAt          DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt          DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt          DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  company         Company        @relation(fields: [companyId], references: [id], onDelete: Cascade)
  recurrenceType  RecurrenceType @relation(fields: [recurrenceTypeId], references: [id])
  entity          Entity?        @relation(fields: [entityId], references: [id])
  currency        Currency       @relation(fields: [currencyId], references: [id])
  category        Category?      @relation(fields: [categoryId], references: [id])
  project         Project?       @relation(fields: [projectId], references: [id])
  paymentMethod   PaymentMethod? @relation(fields: [paymentMethodId], references: [id])
  bankAccount     BankAccount?   @relation(fields: [bankAccountId], references: [id], onDelete: SetNull)

  @@map("recurring_schedules")
}

model CompanyInvitation {
  id        String    @id @default(uuid()) @db.Uuid
  token     String    @unique @db.VarChar(255)
  email     String    @db.VarChar(255)
  companyId String    @map("company_id") @db.Uuid
  roleId    String    @map("role_id") @db.Uuid
  status    String    @default("pending") @db.VarChar(20) // pending, accepted, expired, revoked
  expiresAt DateTime  @map("expires_at") @db.Timestamptz
  invitedBy String    @map("invited_by") @db.Uuid
  message   String?   @db.Text
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  role      Role    @relation(fields: [roleId], references: [id], onDelete: Cascade)
  inviter   User    @relation("InvitationsSent", fields: [invitedBy], references: [id], onDelete: Cascade)

  @@map("company_invitations")
}

model CompanyCode {
  id            String    @id @default(uuid()) @db.Uuid
  code          String    @unique @db.VarChar(20)
  companyId     String    @map("company_id") @db.Uuid
  defaultRoleId String    @map("default_role_id") @db.Uuid
  isActive      Boolean   @default(true) @map("is_active")
  expiresAt     DateTime? @map("expires_at") @db.Timestamptz
  description   String?   @db.Text
  usageCount    Int       @default(0) @map("usage_count")
  maxUsage      Int?      @map("max_usage")
  createdBy     String    @map("created_by") @db.Uuid
  createdAt     DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt     DateTime  @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  company     Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  defaultRole Role    @relation(fields: [defaultRoleId], references: [id], onDelete: Cascade)
  creator     User    @relation("CompanyCodesCreated", fields: [createdBy], references: [id], onDelete: Cascade)

  @@map("company_codes")
}
