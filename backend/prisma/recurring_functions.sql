-- Função para calcular próxima data de recorrência
CREATE OR REPLACE FUNCTION calculate_next_recurring_date(
  schedule_id UUID
) RETURNS TIMESTAMPTZ AS $$
DECLARE
  rec_schedule RECORD;
  next_date TIMESTAMPTZ;
  rec_type VARCHAR;
BEGIN
  -- Buscar dados do agendamento
  SELECT rs.*, rt.name as recurrence_type_name
  INTO rec_schedule
  FROM recurring_schedules rs
  JOIN recurrence_types rt ON rs.recurrence_type_id = rt.id
  WHERE rs.id = schedule_id;
  
  -- Data base para cálculo será a última data de geração ou a data de início
  next_date := rec_schedule.next_generation_date;
  rec_type := rec_schedule.recurrence_type_name;
  
  -- Calcular próxima data com base no tipo de recorrência
  IF rec_type = 'diario' THEN
    next_date := next_date + INTERVAL '1 day';
  ELSIF rec_type = 'semanal' THEN
    next_date := next_date + INTERVAL '1 week';
  ELSIF rec_type = 'quinzenal' THEN
    next_date := next_date + INTERVAL '15 days';
  ELSIF rec_type = 'mensal' THEN
    next_date := next_date + INTERVAL '1 month';
    -- Ajuste para dia específico do mês se configurado
    IF rec_schedule.day_of_month IS NOT NULL THEN
      next_date := date_trunc('month', next_date) + ((rec_schedule.day_of_month - 1) || ' days')::interval;
    END IF;
  ELSIF rec_type = 'bimestral' THEN
    next_date := next_date + INTERVAL '2 months';
    IF rec_schedule.day_of_month IS NOT NULL THEN
      next_date := date_trunc('month', next_date) + ((rec_schedule.day_of_month - 1) || ' days')::interval;
    END IF;
  ELSIF rec_type = 'trimestral' THEN
    next_date := next_date + INTERVAL '3 months';
    IF rec_schedule.day_of_month IS NOT NULL THEN
      next_date := date_trunc('month', next_date) + ((rec_schedule.day_of_month - 1) || ' days')::interval;
    END IF;
  ELSIF rec_type = 'semestral' THEN
    next_date := next_date + INTERVAL '6 months';
    IF rec_schedule.day_of_month IS NOT NULL THEN
      next_date := date_trunc('month', next_date) + ((rec_schedule.day_of_month - 1) || ' days')::interval;
    END IF;
  ELSIF rec_type = 'anual' THEN
    next_date := next_date + INTERVAL '1 year';
    IF rec_schedule.day_of_month IS NOT NULL THEN
      next_date := date_trunc('month', next_date) + ((rec_schedule.day_of_month - 1) || ' days')::interval;
    END IF;
  END IF;
  
  -- Verificar se a data não ultrapassa a data de fim (se existir)
  IF rec_schedule.end_date IS NOT NULL AND next_date > rec_schedule.end_date THEN
    RETURN NULL; -- Indica que não haverã mais recorrências
  END IF;
  
  RETURN next_date;
END;
$$ LANGUAGE plpgsql;

-- Função para gerar contas recorrentes agendadas
CREATE OR REPLACE FUNCTION generate_recurring_accounts() RETURNS INTEGER AS $$
DECLARE
  rec_schedule RECORD;
  new_id UUID;
  accounts_created INTEGER := 0;
BEGIN
  -- Processar todos os agendamentos ativos que já estão no prazo
  FOR rec_schedule IN
    SELECT *
    FROM recurring_schedules
    WHERE active = true
      AND next_generation_date <= CURRENT_TIMESTAMP
      AND (end_date IS NULL OR end_date >= CURRENT_TIMESTAMP)
      AND deleted_at IS NULL
  LOOP
    -- Gerar nova conta com base no tipo (a pagar ou a receber)
    IF rec_schedule.reference_table = 'accounts_payable' THEN
      INSERT INTO accounts_payable (
        company_id, description, entity_id, due_date, amount, status,
        category_id, project_id, payment_method_id, bank_account_id, currency_id
      ) VALUES (
        rec_schedule.company_id, rec_schedule.description, rec_schedule.entity_id,
        rec_schedule.next_generation_date, rec_schedule.amount, 'open',
        rec_schedule.category_id, rec_schedule.project_id, rec_schedule.payment_method_id,
        rec_schedule.bank_account_id, rec_schedule.currency_id
      )
      RETURNING id INTO new_id;
      
    ELSIF rec_schedule.reference_table = 'accounts_receivable' THEN
      INSERT INTO accounts_receivable (
        company_id, description, entity_id, due_date, amount, status,
        category_id, project_id, payment_method_id, bank_account_id, currency_id
      ) VALUES (
        rec_schedule.company_id, rec_schedule.description, rec_schedule.entity_id,
        rec_schedule.next_generation_date, rec_schedule.amount, 'open',
        rec_schedule.category_id, rec_schedule.project_id, rec_schedule.payment_method_id,
        rec_schedule.bank_account_id, rec_schedule.currency_id
      )
      RETURNING id INTO new_id;
    END IF;
    
    accounts_created := accounts_created + 1;
    
    -- Calcular próxima data de geração
    UPDATE recurring_schedules
    SET next_generation_date = calculate_next_recurring_date(id),
        active = CASE WHEN calculate_next_recurring_date(id) IS NULL THEN false ELSE true END
    WHERE id = rec_schedule.id;
  END LOOP;
  
  RETURN accounts_created;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION generate_recurring_accounts() IS 'Função para ser executada diariamente via cron para gerar contas recorrentes';
