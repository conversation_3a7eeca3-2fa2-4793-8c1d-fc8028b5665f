-- Script DML mínimo para popular apenas dados essenciais do FluxoMax
-- Criado em: 06/04/2025
-- ATUALIZADO: Removidos todos os dados mockados/demonstração

-- =============================================
-- INSERÇÃO APENAS DE DADOS ESSENCIAIS (CONFIGURAÇÕES DO SISTEMA)
-- =============================================
-- Este script contém apenas dados necessários para o funcionamento básico
-- da aplicação, sem dados de demonstração ou teste

-- Inserir Tipos de Endereço
INSERT INTO address_types (id, name, description)
VALUES
(gen_random_uuid(), 'Comercial', 'Endereço comercial'),
(gen_random_uuid(), 'Residencial', 'Endereço residencial'),
(gen_random_uuid(), 'Entrega', 'Endereço de entrega'),
(gen_random_uuid(), 'Faturamento', 'Endereço de faturamento'),
(gen_random_uuid(), 'Outro', 'Outro tipo de endereço')
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description;

-- Inserir Moedas Padrão
INSERT INTO currencies (id, code, name, symbol, decimal_places, is_default, created_at, updated_at)
VALUES
(gen_random_uuid(), 'BRL', 'Real Brasileiro', 'R$', 2, true, NOW(), NOW()),
(gen_random_uuid(), 'USD', 'Dólar Americano', '$', 2, false, NOW(), NOW()),
(gen_random_uuid(), 'EUR', 'Euro', '€', 2, false, NOW(), NOW())
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    symbol = EXCLUDED.symbol,
    updated_at = NOW();

-- Inserir Métodos de Pagamento
INSERT INTO payment_methods (id, name, description, created_at, updated_at)
VALUES
(gen_random_uuid(), 'dinheiro', 'Pagamento em dinheiro', NOW(), NOW()),
(gen_random_uuid(), 'pix', 'Transferência via PIX', NOW(), NOW()),
(gen_random_uuid(), 'cartao_credito', 'Pagamento com cartão de crédito', NOW(), NOW()),
(gen_random_uuid(), 'cartao_debito', 'Pagamento com cartão de débito', NOW(), NOW()),
(gen_random_uuid(), 'boleto', 'Pagamento via boleto bancário', NOW(), NOW()),
(gen_random_uuid(), 'transferencia', 'Transferência bancária', NOW(), NOW()),
(gen_random_uuid(), 'cheque', 'Pagamento com cheque', NOW(), NOW()),
(gen_random_uuid(), 'debito_automatico', 'Débito automático em conta', NOW(), NOW())
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description,
    updated_at = NOW();

-- Inserir Tipos de Recorrência
INSERT INTO recurrence_types (id, name, description, created_at, updated_at)
VALUES
(gen_random_uuid(), 'unico', 'Pagamento/recebimento único', NOW(), NOW()),
(gen_random_uuid(), 'diario', 'Recorrência diária', NOW(), NOW()),
(gen_random_uuid(), 'semanal', 'Recorrência semanal', NOW(), NOW()),
(gen_random_uuid(), 'quinzenal', 'Recorrência quinzenal', NOW(), NOW()),
(gen_random_uuid(), 'mensal', 'Recorrência mensal', NOW(), NOW()),
(gen_random_uuid(), 'bimestral', 'Recorrência bimestral', NOW(), NOW()),
(gen_random_uuid(), 'trimestral', 'Recorrência trimestral', NOW(), NOW()),
(gen_random_uuid(), 'semestral', 'Recorrência semestral', NOW(), NOW()),
(gen_random_uuid(), 'anual', 'Recorrência anual', NOW(), NOW())
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description,
    updated_at = NOW();

-- Inserir Bancos
INSERT INTO banks (id, code, name, created_at, updated_at)
VALUES
(gen_random_uuid(), '001', 'Banco do Brasil S.A.', NOW(), NOW()),
(gen_random_uuid(), '237', 'Banco Bradesco S.A.', NOW(), NOW()),
(gen_random_uuid(), '341', 'Itaú Unibanco S.A.', NOW(), NOW()),
(gen_random_uuid(), '104', 'Caixa Econômica Federal', NOW(), NOW()),
(gen_random_uuid(), '033', 'Banco Santander (Brasil) S.A.', NOW(), NOW()),
(gen_random_uuid(), '260', 'Nu Pagamentos S.A. (Nubank)', NOW(), NOW())
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    updated_at = NOW();

-- =============================================
-- CONFIGURAÇÃO DE PERMISSÕES DO SISTEMA
-- =============================================

-- Inserir permissões básicas do sistema
INSERT INTO system_permissions (id, name, description, resource, action, created_at, updated_at)
VALUES
(gen_random_uuid(), 'companies.read', 'Visualizar empresas', 'companies', 'read', NOW(), NOW()),
(gen_random_uuid(), 'companies.write', 'Criar/editar empresas', 'companies', 'write', NOW(), NOW()),
(gen_random_uuid(), 'users.read', 'Visualizar usuários', 'users', 'read', NOW(), NOW()),
(gen_random_uuid(), 'users.write', 'Criar/editar usuários', 'users', 'write', NOW(), NOW()),
(gen_random_uuid(), 'accounts.read', 'Visualizar contas', 'accounts', 'read', NOW(), NOW()),
(gen_random_uuid(), 'accounts.write', 'Criar/editar contas', 'accounts', 'write', NOW(), NOW()),
(gen_random_uuid(), 'transactions.read', 'Visualizar transações', 'transactions', 'read', NOW(), NOW()),
(gen_random_uuid(), 'transactions.write', 'Criar/editar transações', 'transactions', 'write', NOW(), NOW()),
(gen_random_uuid(), 'reports.read', 'Visualizar relatórios', 'reports', 'read', NOW(), NOW()),
(gen_random_uuid(), 'settings.read', 'Visualizar configurações', 'settings', 'read', NOW(), NOW()),
(gen_random_uuid(), 'settings.write', 'Editar configurações', 'settings', 'write', NOW(), NOW())
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description,
    updated_at = NOW();

-- =============================================
-- VERIFICAÇÃO FINAL
-- =============================================

-- Verificar se os dados essenciais foram inseridos corretamente
DO $$
DECLARE
    address_type_count INTEGER;
    currency_count INTEGER;
    payment_method_count INTEGER;
    recurrence_type_count INTEGER;
    bank_count INTEGER;
    system_permission_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO address_type_count FROM address_types;
    SELECT COUNT(*) INTO currency_count FROM currencies;
    SELECT COUNT(*) INTO payment_method_count FROM payment_methods;
    SELECT COUNT(*) INTO recurrence_type_count FROM recurrence_types;
    SELECT COUNT(*) INTO bank_count FROM banks;
    SELECT COUNT(*) INTO system_permission_count FROM system_permissions;

    RAISE NOTICE 'Dados essenciais inseridos:';
    RAISE NOTICE '- Tipos de endereço: %', address_type_count;
    RAISE NOTICE '- Moedas: %', currency_count;
    RAISE NOTICE '- Métodos de pagamento: %', payment_method_count;
    RAISE NOTICE '- Tipos de recorrência: %', recurrence_type_count;
    RAISE NOTICE '- Bancos: %', bank_count;
    RAISE NOTICE '- Permissões do sistema: %', system_permission_count;

    IF address_type_count = 0 OR currency_count = 0 OR payment_method_count = 0 OR 
       recurrence_type_count = 0 OR bank_count = 0 OR system_permission_count = 0 THEN
        RAISE WARNING 'Alguns dados essenciais podem não ter sido inseridos corretamente.';
    ELSE
        RAISE NOTICE 'Seed de dados essenciais concluído com sucesso!';
        RAISE NOTICE 'A aplicação está pronta para uso com dados reais.';
    END IF;
END;
$$;
