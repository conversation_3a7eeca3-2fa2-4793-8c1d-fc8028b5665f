import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString, IsUUID, IsEnum } from 'class-validator';

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
}

export class CompanyInvitationDto {
  @ApiProperty({
    description: 'ID único do convite',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Token único do convite',
    example: 'abc123def456ghi789',
  })
  token: string;

  @ApiProperty({
    description: 'Email do convidado',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  companyId: string;

  @ApiProperty({
    description: 'ID do papel/função',
    example: '123e4567-e89b-12d3-a456-************',
  })
  roleId: string;

  @ApiProperty({
    description: 'Status do convite',
    enum: InvitationStatus,
  })
  status: InvitationStatus;

  @ApiProperty({
    description: 'Data de expiração',
    example: '2023-12-31T23:59:59.000Z',
  })
  expiresAt: Date;

  @ApiProperty({
    description: 'ID do usuário que enviou o convite',
    example: '123e4567-e89b-12d3-a456-************',
  })
  invitedBy: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class CreateCompanyInvitationDto {
  @ApiProperty({
    description: 'Email do usuário a ser convidado',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Email inválido' })
  @IsNotEmpty({ message: 'Email não pode ser vazio' })
  email: string;

  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID(4, { message: 'ID da empresa deve ser um UUID válido' })
  @IsNotEmpty({ message: 'ID da empresa não pode ser vazio' })
  companyId: string;

  @ApiProperty({
    description: 'ID do papel/função',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID(4, { message: 'ID do papel deve ser um UUID válido' })
  @IsNotEmpty({ message: 'ID do papel não pode ser vazio' })
  roleId: string;

  @ApiProperty({
    description: 'Mensagem personalizada do convite (opcional)',
    example: 'Venha fazer parte da nossa equipe!',
    required: false,
  })
  @IsString({ message: 'Mensagem deve ser uma string' })
  @IsOptional()
  message?: string;
}

export class AcceptInvitationDto {
  @ApiProperty({
    description: 'Token do convite',
    example: 'abc123def456ghi789',
  })
  @IsString({ message: 'Token deve ser uma string' })
  @IsNotEmpty({ message: 'Token não pode ser vazio' })
  token: string;
}

export class InvitationValidationDto {
  @ApiProperty({
    description: 'Token do convite',
    example: 'abc123def456ghi789',
  })
  token: string;

  @ApiProperty({
    description: 'Indica se o convite é válido',
    example: true,
  })
  isValid: boolean;

  @ApiProperty({
    description: 'Nome da empresa',
    example: 'Empresa ABC Ltda',
  })
  companyName?: string;

  @ApiProperty({
    description: 'Nome do papel/função',
    example: 'Gerente',
  })
  roleName?: string;

  @ApiProperty({
    description: 'Email do convidado',
    example: '<EMAIL>',
  })
  email?: string;

  @ApiProperty({
    description: 'Data de expiração',
    example: '2023-12-31T23:59:59.000Z',
  })
  expiresAt?: Date;

  @ApiProperty({
    description: 'Mensagem de erro (se inválido)',
    example: 'Convite expirado',
  })
  errorMessage?: string;
}

export class CompanyInvitationListDto {
  @ApiProperty({
    description: 'Lista de convites',
    type: [CompanyInvitationDto],
  })
  items: CompanyInvitationDto[];

  @ApiProperty({ description: 'Total de registros', example: 10 })
  total: number;

  @ApiProperty({ description: 'Página atual', example: 1 })
  page: number;

  @ApiProperty({ description: 'Limite de itens por página', example: 10 })
  limit: number;
}
