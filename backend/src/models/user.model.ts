import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  Is<PERSON><PERSON>al,
  IsString,
  <PERSON>U<PERSON><PERSON>,
  <PERSON><PERSON>eng<PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import {
  CreateProfileDto,
  ProfileDto,
  UpdateProfileDto,
} from './profile.model';

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  WITHOUT_COMPANY = 'without_company',
}

export class User {
  id: string;
  email: string;
  password: string;
  status: UserStatus;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  profile?: any;
}

export class UserDto {
  @ApiProperty({ description: 'ID único do usuário' })
  id: string;

  @ApiProperty({ description: 'Email do usuário' })
  email: string;

  @ApiProperty({ description: 'Status do usuário', enum: UserStatus })
  status: UserStatus;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: Date;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: Date;

  @ApiProperty({ description: 'Perfil do usuário', type: ProfileDto })
  profile?: ProfileDto;
}

export class CreateUserDto {
  @ApiProperty({
    description: 'Email do usuário',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Email inválido' })
  @IsNotEmpty({ message: 'Email não pode ser vazio' })
  email: string;

  @ApiProperty({
    description: 'Senha do usuário (mínimo 6 caracteres)',
    example: 'Admin123',
    minLength: 6,
  })
  @IsString({ message: 'Senha deve ser uma string' })
  @IsNotEmpty({ message: 'Senha não pode ser vazia' })
  @MinLength(6, { message: 'Senha deve ter no mínimo 6 caracteres' })
  password: string;

  @ApiProperty({
    description: 'Status do usuário',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
    required: false,
  })
  @IsEnum(UserStatus, { message: 'Status inválido' })
  @IsOptional()
  status?: UserStatus = UserStatus.ACTIVE;

  @ApiProperty({
    description: 'Dados do perfil do usuário',
    type: CreateProfileDto,
  })
  @IsNotEmpty({ message: 'Dados do perfil não podem ser vazios' })
  profile: CreateProfileDto;
}

export class UpdateUserDto {
  @ApiProperty({
    description: 'Email do usuário',
    example: '<EMAIL>',
    required: false,
  })
  @IsEmail({}, { message: 'Email inválido' })
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: 'Senha do usuário (mínimo 6 caracteres)',
    example: 'Admin123',
    minLength: 6,
    required: false,
  })
  @IsString({ message: 'Senha deve ser uma string' })
  @MinLength(6, { message: 'Senha deve ter no mínimo 6 caracteres' })
  @IsOptional()
  password?: string;

  @ApiProperty({
    description: 'Status do usuário',
    enum: UserStatus,
    required: false,
  })
  @IsEnum(UserStatus, { message: 'Status inválido' })
  @IsOptional()
  status?: UserStatus;
}

export class UserProfileDto extends UserDto {
  @ApiProperty({ description: 'Perfil do usuário', type: ProfileDto })
  declare profile: ProfileDto;
}

export class LoginDto {
  @ApiProperty({
    description: 'Email do usuário',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Email inválido' })
  @IsNotEmpty({ message: 'Email não pode ser vazio' })
  email: string;

  @ApiProperty({
    description: 'Senha do usuário',
    example: 'Admin123',
  })
  @IsString({ message: 'Senha deve ser uma string' })
  @IsNotEmpty({ message: 'Senha não pode ser vazia' })
  password: string;
}
