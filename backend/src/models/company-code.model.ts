import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID, IsBoolean, IsDateString } from 'class-validator';

export class CompanyCodeDto {
  @ApiProperty({
    description: 'ID único do código',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Código de acesso à empresa',
    example: 'ABC123XYZ',
  })
  code: string;

  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  companyId: string;

  @ApiProperty({
    description: 'ID do papel padrão para novos usuários',
    example: '123e4567-e89b-12d3-a456-************',
  })
  defaultRoleId: string;

  @ApiProperty({
    description: 'Indica se o código está ativo',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Data de expiração (opcional)',
    example: '2023-12-31T23:59:59.000Z',
    required: false,
  })
  expiresAt?: Date;

  @ApiProperty({
    description: 'Descrição do código',
    example: 'Código para novos funcionários',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Número de usos do código',
    example: 5,
  })
  usageCount: number;

  @ApiProperty({
    description: 'Limite máximo de usos (opcional)',
    example: 100,
    required: false,
  })
  maxUsage?: number;

  @ApiProperty({
    description: 'ID do usuário que criou o código',
    example: '123e4567-e89b-12d3-a456-************',
  })
  createdBy: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class CreateCompanyCodeDto {
  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID(4, { message: 'ID da empresa deve ser um UUID válido' })
  @IsNotEmpty({ message: 'ID da empresa não pode ser vazio' })
  companyId: string;

  @ApiProperty({
    description: 'ID do papel padrão para novos usuários',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID(4, { message: 'ID do papel deve ser um UUID válido' })
  @IsNotEmpty({ message: 'ID do papel não pode ser vazio' })
  defaultRoleId: string;

  @ApiProperty({
    description: 'Descrição do código (opcional)',
    example: 'Código para novos funcionários',
    required: false,
  })
  @IsString({ message: 'Descrição deve ser uma string' })
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Data de expiração (opcional)',
    example: '2023-12-31T23:59:59.000Z',
    required: false,
  })
  @IsDateString({}, { message: 'Data de expiração deve ser uma data válida' })
  @IsOptional()
  expiresAt?: string;

  @ApiProperty({
    description: 'Limite máximo de usos (opcional)',
    example: 100,
    required: false,
  })
  @IsOptional()
  maxUsage?: number;
}

export class JoinCompanyByCodeDto {
  @ApiProperty({
    description: 'Código de acesso à empresa',
    example: 'ABC123XYZ',
  })
  @IsString({ message: 'Código deve ser uma string' })
  @IsNotEmpty({ message: 'Código não pode ser vazio' })
  code: string;
}

export class UpdateCompanyCodeDto {
  @ApiProperty({
    description: 'Indica se o código está ativo',
    example: true,
    required: false,
  })
  @IsBoolean({ message: 'Status ativo deve ser um booleano' })
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    description: 'Descrição do código',
    example: 'Código para novos funcionários',
    required: false,
  })
  @IsString({ message: 'Descrição deve ser uma string' })
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Data de expiração',
    example: '2023-12-31T23:59:59.000Z',
    required: false,
  })
  @IsDateString({}, { message: 'Data de expiração deve ser uma data válida' })
  @IsOptional()
  expiresAt?: string;

  @ApiProperty({
    description: 'Limite máximo de usos',
    example: 100,
    required: false,
  })
  @IsOptional()
  maxUsage?: number;
}

export class CompanyCodeValidationDto {
  @ApiProperty({
    description: 'Código de acesso',
    example: 'ABC123XYZ',
  })
  code: string;

  @ApiProperty({
    description: 'Indica se o código é válido',
    example: true,
  })
  isValid: boolean;

  @ApiProperty({
    description: 'Nome da empresa',
    example: 'Empresa ABC Ltda',
  })
  companyName?: string;

  @ApiProperty({
    description: 'Nome do papel padrão',
    example: 'Funcionário',
  })
  roleName?: string;

  @ApiProperty({
    description: 'Mensagem de erro (se inválido)',
    example: 'Código expirado ou inválido',
  })
  errorMessage?: string;
}

export class CompanyCodeListDto {
  @ApiProperty({
    description: 'Lista de códigos',
    type: [CompanyCodeDto],
  })
  items: CompanyCodeDto[];

  @ApiProperty({ description: 'Total de registros', example: 10 })
  total: number;

  @ApiProperty({ description: 'Página atual', example: 1 })
  page: number;

  @ApiProperty({ description: 'Limite de itens por página', example: 10 })
  limit: number;
}
