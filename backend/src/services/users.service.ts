import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import {
  CreateUserDto,
  UpdateUserDto,
  UserDto,
  UserProfileDto,
  UserStatus,
} from '../models/user.model';
import { ProfileDto, UpdateProfileDto } from '../models/profile.model';
import * as bcrypt from 'bcrypt';
import { Prisma } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

// Interfaces para resultados de consultas SQL nativas
interface UserRawResult {
  id: string;
  email: string;
  password?: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  username?: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  avatar_url?: string;
  preferences?: any;
  is_active?: boolean;
}

interface ProfileRawResult {
  id: string;
  username: string;
  first_name: string;
  last_name: string;
  phone: string;
  avatar_url: string;
  preferences: any;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);
  constructor(private prisma: PrismaService) {}

  async create(createUserDto: CreateUserDto): Promise<UserDto> {
    // Verificar se o email já existe
    const existingUserByEmail = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT * FROM "users" WHERE email = ${createUserDto.email} AND deleted_at IS NULL
    `;

    if (existingUserByEmail.length > 0) {
      throw new ConflictException('Email já existe');
    }

    // Verificar se o username já existe
    if (createUserDto.profile?.username) {
      const existingUserByUsername = await this.prisma.$queryRaw<
        ProfileRawResult[]
      >`
        SELECT * FROM "profiles" WHERE username = ${createUserDto.profile.username} AND deleted_at IS NULL
      `;

      if (existingUserByUsername.length > 0) {
        throw new ConflictException('Nome de usuário já existe');
      }
    }

    // Criptografar senha
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    // Criar usuário e perfil em uma transação
    return await this.prisma.$transaction(async (tx) => {
      // Gerar UUID para o novo usuário
      const newUserId = uuidv4();

      // Criar usuário usando SQL nativo para evitar problemas de tipagem
      // Novos usuários começam com status 'sem_empresa' por padrão
      const userInsert = await tx.$executeRaw`
        INSERT INTO "users" (id, email, password, status, created_at, updated_at)
        VALUES (${newUserId}::uuid, ${createUserDto.email}, ${hashedPassword}, ${createUserDto.status || 'sem_empresa'}, NOW(), NOW())
        RETURNING id
      `;

      // Verificar se o usuário foi criado com sucesso
      if (!newUserId) {
        throw new BadRequestException('Falha ao criar usuário');
      }

      // Criar perfil usando SQL nativo
      await tx.$executeRaw`
        INSERT INTO "profiles" (id, username, first_name, last_name, phone, avatar_url, preferences, is_active, created_at, updated_at)
        VALUES (
          ${newUserId}::uuid,
          ${createUserDto.profile?.username || null},
          ${createUserDto.profile?.firstName || null},
          ${createUserDto.profile?.lastName || null},
          ${createUserDto.profile?.phone || null},
          ${createUserDto.profile?.avatarUrl || null},
          ${createUserDto.profile?.preferences || null},
          ${createUserDto.profile?.isActive === false ? false : true},
          NOW(),
          NOW()
        )
      `;

      // Buscar o usuário e perfil criados
      const userResult = await tx.$queryRaw`
        SELECT u.id, u.email, u.status, u.created_at, u.updated_at,
               p.username, p.first_name, p.last_name, p.phone, p.avatar_url, p.preferences, p.is_active
        FROM "users" u
        LEFT JOIN "profiles" p ON u.id = p.id
        WHERE u.id = ${newUserId}::uuid
      `;

      const createdUser =
        Array.isArray(userResult) && userResult.length > 0
          ? userResult[0]
          : null;

      if (!createdUser) {
        throw new BadRequestException('Falha ao recuperar usuário criado');
      }

      // Retornar o usuário criado no formato esperado
      return {
        id: createdUser.id,
        email: createdUser.email,
        status: createdUser.status as UserStatus,
        createdAt: createdUser.created_at,
        updatedAt: createdUser.updated_at,
        profile: {
          id: createdUser.id,
          username: createdUser.username,
          firstName: createdUser.first_name,
          lastName: createdUser.last_name,
          phone: createdUser.phone,
          avatarUrl: createdUser.avatar_url,
          preferences: createdUser.preferences,
          isActive: createdUser.is_active,
        },
      };
    });
  }

  async findByEmail(email: string): Promise<UserDto> {
    const userResult = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT u.id, u.email, u.status, u.created_at, u.updated_at,
             p.username, p.first_name, p.last_name, p.phone, p.avatar_url, p.preferences, p.is_active
      FROM "users" u
      LEFT JOIN "profiles" p ON u.id = p.id
      WHERE u.email = ${email} AND u.deleted_at IS NULL
    `;

    const user = userResult.length > 0 ? userResult[0] : null;

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    return {
      id: user.id,
      email: user.email,
      status: user.status as UserStatus,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      profile: {
        id: user.id,
        username: user.username ?? '',
        firstName: user.first_name ?? '',
        lastName: user.last_name ?? '',
        phone: user.phone ?? '',
        avatarUrl: user.avatar_url ?? '',
        preferences: user.preferences ?? {},
        isActive: user.is_active ?? false,
      },
    };
  }

  async findAll(companyId: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    // Usando a abordagem correta com Prisma para incluir relações aninhadas
    const userCompanyRoles = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT ucr.*, u.*, p.*
      FROM "user_company_roles" ucr
      JOIN "users" u ON ucr.user_id = u.id
      LEFT JOIN "profiles" p ON u.id = p.id
      WHERE ucr.company_id = ${companyId}::uuid
      AND u.deleted_at IS NULL
      LIMIT ${limit} OFFSET ${skip}
    `;

    // Transformando os resultados para o formato esperado
    const users = userCompanyRoles.map((row: UserRawResult) => ({
      id: row.id,
      email: row.email,
      status: row.status as UserStatus,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      profile: row.username
        ? {
            id: row.id,
            username: row.username ?? '',
            firstName: row.first_name ?? '',
            lastName: row.last_name ?? '',
            phone: row.phone ?? '',
            avatarUrl: row.avatar_url ?? '',
            preferences: row.preferences ?? {},
            isActive: row.is_active ?? false,
          }
        : undefined,
    }));

    // Contando o total de usuários para paginação
    const totalResult = await this.prisma.$queryRaw<{ total: string }[]>`
      SELECT COUNT(*) as total
      FROM "user_company_roles" ucr
      JOIN "users" u ON ucr.user_id = u.id
      WHERE ucr.company_id = ${companyId}::uuid
      AND u.deleted_at IS NULL
    `;

    const total = totalResult.length > 0 ? Number(totalResult[0].total) : 0;

    return {
      data: users,
      meta: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string): Promise<UserDto> {
    const userResult = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT u.id, u.email, u.status, u.created_at, u.updated_at,
             p.username, p.first_name, p.last_name, p.phone, p.avatar_url, p.preferences, p.is_active
      FROM "users" u
      LEFT JOIN "profiles" p ON u.id = p.id
      WHERE u.id = ${id}::uuid AND u.deleted_at IS NULL
    `;

    const user = userResult.length > 0 ? userResult[0] : null;

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    return {
      id: user.id,
      email: user.email,
      status: user.status as UserStatus,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      profile: user.username
        ? {
            id: user.id,
            username: user.username ?? '',
            firstName: user.first_name ?? '',
            lastName: user.last_name ?? '',
            phone: user.phone ?? '',
            avatarUrl: user.avatar_url ?? '',
            preferences: user.preferences ?? {},
            isActive: user.is_active ?? false,
          }
        : undefined,
    };
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<UserDto> {
    // Verificar se o usuário existe
    const existingUserResult = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT * FROM "users" WHERE id = ${id}::uuid AND deleted_at IS NULL
    `;

    const existingUser =
      Array.isArray(existingUserResult) && existingUserResult.length > 0
        ? existingUserResult[0]
        : null;

    if (!existingUser) {
      throw new NotFoundException('Usuário não encontrado');
    }

    // Verificar se o email já existe (se estiver sendo atualizado)
    if (updateUserDto.email && updateUserDto.email !== existingUser.email) {
      const emailExistsResult = await this.prisma.$queryRaw<UserRawResult[]>`
        SELECT * FROM "users" WHERE email = ${updateUserDto.email} AND deleted_at IS NULL
      `;

      const emailExists = emailExistsResult.length > 0;

      if (emailExists) {
        throw new ConflictException('Email já existe');
      }
    }

    // Preparar dados para atualização
    let updateQuery = 'UPDATE "users" SET ';
    const updateValues: string[] = [];
    const queryParams: any[] = [];

    if (updateUserDto.email) {
      updateValues.push(`email = $${updateValues.length + 1}`);
      queryParams.push(updateUserDto.email);
    }

    if (updateUserDto.status) {
      updateValues.push(`status = $${updateValues.length + 1}`);
      queryParams.push(updateUserDto.status);
    }

    if (updateUserDto.password) {
      const hashedPassword = await bcrypt.hash(updateUserDto.password, 10);
      updateValues.push(`password = $${updateValues.length + 1}`);
      queryParams.push(hashedPassword);
    }

    updateValues.push(`updated_at = $${updateValues.length + 1}`);
    queryParams.push(new Date());

    if (updateValues.length === 1) {
      // Somente updated_at foi adicionado, não há outras atualizações
      throw new BadRequestException('Nenhum dado para atualizar');
    }

    updateQuery += updateValues.join(', ');
    updateQuery += ` WHERE id = $${queryParams.length + 1}::uuid RETURNING *`;
    queryParams.push(id);

    // Atualizar o usuário
    try {
      this.logger.debug(`Executando query de atualização de usuário: ${updateQuery}`);
      this.logger.debug(`Valores: ${JSON.stringify(queryParams)}`);

      const updatedUserResult = await this.prisma.$queryRawUnsafe<
        UserRawResult[]
      >(updateQuery, ...queryParams);

      const updatedUserData =
        updatedUserResult.length > 0 ? updatedUserResult[0] : null;

      if (!updatedUserData) {
        throw new NotFoundException('Falha ao atualizar usuário');
      }

      // Buscar o perfil atualizado
      const profileResult = await this.prisma.$queryRaw<ProfileRawResult[]>`
        SELECT * FROM "profiles" WHERE id = ${id}::uuid AND deleted_at IS NULL
      `;

      const profile = profileResult.length > 0 ? profileResult[0] : null;

      return {
        id: updatedUserData.id,
        email: updatedUserData.email,
        status: updatedUserData.status as UserStatus,
        createdAt: updatedUserData.created_at,
        updatedAt: updatedUserData.updated_at,
        profile: profile
          ? {
              id: profile.id,
              username: profile.username,
              firstName: profile.first_name,
              lastName: profile.last_name,
              phone: profile.phone,
              avatarUrl: profile.avatar_url,
              preferences: profile.preferences,
              isActive: profile.is_active ?? false,
            }
          : undefined,
      };
    } catch (dbError) {
      this.logger.error(`Erro na execução da query de atualização de usuário: ${dbError.message}`, dbError.stack);
      throw new BadRequestException(`Erro ao atualizar usuário: ${dbError.message}`);
    }
  }

  async remove(id: string): Promise<void> {
    // Verificar se o usuário existe
    const existingUserResult = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT * FROM "users" WHERE id = ${id}::uuid AND deleted_at IS NULL
    `;

    const existingUser =
      Array.isArray(existingUserResult) && existingUserResult.length > 0
        ? existingUserResult[0]
        : null;

    if (!existingUser) {
      throw new NotFoundException('Usuário não encontrado');
    }

    // Realizar soft delete em uma transação
    const now = new Date();
    await this.prisma.$transaction([
      // Atualizar deletedAt no usuário
      this.prisma.$executeRaw`
        UPDATE "users" SET deleted_at = ${now} WHERE id = ${id}::uuid
      `,
      // Atualizar deletedAt no perfil
      this.prisma.$executeRaw`
        UPDATE "profiles" SET deleted_at = ${now} WHERE id = ${id}::uuid
      `,
    ]);
  }

  async getProfile(userId: string): Promise<UserProfileDto> {
    const userResult = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT u.id, u.email, u.status, u.created_at, u.updated_at,
             p.username, p.first_name, p.last_name, p.phone, p.avatar_url, p.preferences, p.is_active
      FROM "users" u
      LEFT JOIN "profiles" p ON u.id = p.id
      WHERE u.id = ${userId}::uuid AND u.deleted_at IS NULL
    `;

    const user = userResult.length > 0 ? userResult[0] : null;

    if (!user) {
      this.logger.error(`Usuário com ID ${userId} não encontrado`);
      throw new NotFoundException('Usuário não encontrado');
    }

    if (!user.username) {
      this.logger.error(`Perfil para o usuário com ID ${userId} não encontrado`);
      throw new NotFoundException('Perfil não encontrado');
    }

    return {
      id: user.id,
      email: user.email,
      status: user.status as UserStatus,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      profile: {
        id: user.id,
        username: user.username ?? '',
        firstName: user.first_name ?? '',
        lastName: user.last_name ?? '',
        phone: user.phone ?? '',
        avatarUrl: user.avatar_url ?? '',
        preferences: user.preferences ?? {},
        isActive: user.is_active ?? false,
      },
    };
  }

  async updateProfile(
    userId: string,
    updateProfileDto: UpdateProfileDto,
  ): Promise<UserProfileDto> {
    // Verificar se o usuário existe
    const existingUserResult = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT u.id, u.email, u.status, u.created_at, u.updated_at,
             p.username, p.first_name, p.last_name, p.phone, p.avatar_url, p.preferences, p.is_active
      FROM "users" u
      LEFT JOIN "profiles" p ON u.id = p.id
      WHERE u.id = ${userId}::uuid AND u.deleted_at IS NULL
    `;

    const existingUser =
      existingUserResult.length > 0 ? existingUserResult[0] : null;

    if (!existingUser) {
      throw new NotFoundException('Usuário não encontrado');
    }

    if (!existingUser.username) {
      throw new NotFoundException('Perfil não encontrado');
    }

    // Verificar se o username já existe (se estiver sendo atualizado)
    if (
      updateProfileDto.username &&
      updateProfileDto.username !== existingUser.username
    ) {
      const usernameExistsResult = await this.prisma.$queryRaw<
        ProfileRawResult[]
      >`
        SELECT * FROM "profiles" WHERE username = ${updateProfileDto.username} AND deleted_at IS NULL
      `;

      const usernameExists = usernameExistsResult.length > 0;

      if (usernameExists) {
        throw new ConflictException('Nome de usuário já existe');
      }
    }

    // Preparar dados para atualização
    let updateQuery = 'UPDATE "profiles" SET ';
    const updateValues: string[] = [];
    const queryParams: any[] = [];

    if (updateProfileDto.username) {
      updateValues.push(`username = $${updateValues.length + 1}`);
      queryParams.push(updateProfileDto.username);
    }

    if (updateProfileDto.firstName !== undefined) {
      updateValues.push(`first_name = $${updateValues.length + 1}`);
      queryParams.push(updateProfileDto.firstName);
    }

    if (updateProfileDto.lastName !== undefined) {
      updateValues.push(`last_name = $${updateValues.length + 1}`);
      queryParams.push(updateProfileDto.lastName);
    }

    if (updateProfileDto.phone !== undefined) {
      updateValues.push(`phone = $${updateValues.length + 1}`);
      queryParams.push(updateProfileDto.phone);
    }

    if (updateProfileDto.avatarUrl !== undefined) {
      updateValues.push(`avatar_url = $${updateValues.length + 1}`);
      queryParams.push(updateProfileDto.avatarUrl);
    }

    if (updateProfileDto.preferences !== undefined) {
      try {
        // Garantir que preferences seja um JSON válido
        const preferencesJson = typeof updateProfileDto.preferences === 'string'
          ? JSON.parse(updateProfileDto.preferences)
          : updateProfileDto.preferences;

        this.logger.debug(`Processando preferences: ${JSON.stringify(preferencesJson)}`);

        updateValues.push(`preferences = $${updateValues.length + 1}`);
        queryParams.push(preferencesJson);
      } catch (error) {
        this.logger.error(`Erro ao processar preferences: ${error.message}`, error.stack);
        throw new BadRequestException('Formato inválido para o campo preferences');
      }
    }

    if (updateProfileDto.isActive !== undefined) {
      updateValues.push(`is_active = $${updateValues.length + 1}`);
      queryParams.push(updateProfileDto.isActive);
    }

    updateValues.push(`updated_at = $${updateValues.length + 1}`);
    queryParams.push(new Date());

    if (updateValues.length === 1) {
      // Somente updated_at foi adicionado, não há outras atualizações
      throw new BadRequestException('Nenhum dado para atualizar');
    }

    updateQuery += updateValues.join(', ');
    updateQuery += ` WHERE id = $${queryParams.length + 1}::uuid RETURNING *`;
    queryParams.push(userId);

    // Atualizar o perfil
    try {
      this.logger.debug(`Executando query: ${updateQuery}`);
      this.logger.debug(`Valores: ${JSON.stringify(queryParams)}`);

      const updatedProfileResult = await this.prisma.$queryRawUnsafe<
        ProfileRawResult[]
      >(updateQuery, ...queryParams);

      const updatedProfile =
        updatedProfileResult.length > 0 ? updatedProfileResult[0] : null;

      if (!updatedProfile) {
        throw new NotFoundException('Falha ao atualizar perfil');
      }

      return {
        id: existingUser.id,
        email: existingUser.email,
        status: existingUser.status as UserStatus,
        createdAt: existingUser.created_at,
        updatedAt: existingUser.updated_at,
        profile: {
          id: updatedProfile.id,
          username: updatedProfile.username ?? '',
          firstName: updatedProfile.first_name ?? '',
          lastName: updatedProfile.last_name ?? '',
          phone: updatedProfile.phone ?? '',
          avatarUrl: updatedProfile.avatar_url ?? '',
          preferences: updatedProfile.preferences ?? {},
          isActive: updatedProfile.is_active ?? false,
        },
      };
    } catch (dbError) {
      this.logger.error(`Erro na execução da query: ${dbError.message}`, dbError.stack);
      throw new BadRequestException(`Erro ao atualizar perfil: ${dbError.message}`);
    }
  }

  async findById(id: string): Promise<UserDto> {
    const userResult = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT u.id, u.email, u.status, u.created_at, u.updated_at,
             p.username, p.first_name, p.last_name, p.phone, p.avatar_url, p.preferences, p.is_active
      FROM "users" u
      LEFT JOIN "profiles" p ON u.id = p.id
      WHERE u.id = ${id}::uuid AND u.deleted_at IS NULL
    `;

    const user = userResult.length > 0 ? userResult[0] : null;

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    return {
      id: user.id,
      email: user.email,
      status: user.status as UserStatus,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      profile: user.username
        ? {
            id: user.id,
            username: user.username ?? '',
            firstName: user.first_name ?? '',
            lastName: user.last_name ?? '',
            phone: user.phone ?? '',
            avatarUrl: user.avatar_url ?? '',
            preferences: user.preferences ?? {},
            isActive: user.is_active ?? false,
          }
        : undefined,
    };
  }

  /**
   * Obtém a empresa padrão do usuário
   * @param userId ID do usuário
   * @returns Objeto com o ID da empresa padrão do usuário ou null se não encontrada
   */
  async getUserDefaultCompany(userId: string): Promise<{ companyId: string } | null> {
    try {
      // Buscar a primeira empresa associada ao usuário
      const userCompanyRole = await this.prisma.userCompanyRole.findFirst({
        where: {
          userId,
          deletedAt: null
        },
        select: {
          companyId: true
        }
      });

      if (!userCompanyRole) {
        this.logger.warn(`Nenhuma empresa encontrada para o usuário ${userId}`);
        return null;
      }

      return { companyId: userCompanyRole.companyId };
    } catch (error) {
      this.logger.error(`Erro ao buscar empresa padrão do usuário: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Obtém todas as empresas associadas ao usuário
   * @param userId ID do usuário
   * @returns Lista de empresas do usuário
   */
  async getUserCompanies(userId: string): Promise<any[]> {
    try {
      const userCompanies = await this.prisma.$queryRaw<any[]>`
        SELECT
          c.id,
          c.name,
          c.cnpj,
          c.logo,
          r.name as role_name,
          r.id as role_id
        FROM user_company_roles ucr
        JOIN companies c ON ucr.company_id = c.id
        JOIN roles r ON ucr.role_id = r.id
        WHERE ucr.user_id = ${userId}::uuid
        AND ucr.deleted_at IS NULL
        AND c.deleted_at IS NULL
        AND r.deleted_at IS NULL
        ORDER BY c.name
      `;

      return userCompanies.map((company) => ({
        id: company.id,
        name: company.name,
        cnpj: company.cnpj,
        logo: company.logo,
        roleName: company.role_name,
        roleId: company.role_id,
      }));
    } catch (error) {
      this.logger.error(`Erro ao buscar empresas do usuário: ${error.message}`, error.stack);
      return [];
    }
  }
}
