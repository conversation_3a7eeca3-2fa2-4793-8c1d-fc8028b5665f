import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { v4 as uuidv4 } from 'uuid';
import { randomBytes } from 'crypto';
import {
  CreateCompanyInvitationDto,
  CompanyInvitationDto,
  AcceptInvitationDto,
  InvitationValidationDto,
  CompanyInvitationListDto,
  InvitationStatus,
} from '../models/company-invitation.model';

@Injectable()
export class CompanyInvitationService {
  private readonly logger = new Logger(CompanyInvitationService.name);

  constructor(private prisma: PrismaService) {}

  /**
   * Cria um novo convite para empresa
   */
  async create(
    createInvitationDto: CreateCompanyInvitationDto,
    invitedBy: string,
  ): Promise<CompanyInvitationDto> {
    try {
      // Verificar se o usuário tem permissão para convidar (deve ser admin da empresa)
      await this.validateUserCanInvite(invitedBy, createInvitationDto.companyId);

      // Verificar se a empresa existe
      const company = await this.prisma.company.findFirst({
        where: {
          id: createInvitationDto.companyId,
          deleted_at: null,
        },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // Verificar se o papel existe
      const role = await this.prisma.role.findFirst({
        where: {
          id: createInvitationDto.roleId,
          companyId: createInvitationDto.companyId,
          deletedAt: null,
        },
      });

      if (!role) {
        throw new NotFoundException('Papel não encontrado ou não pertence à empresa');
      }

      // Verificar se já existe um convite pendente para este email e empresa
      const existingInvitation = await this.prisma.companyInvitation.findFirst({
        where: {
          email: createInvitationDto.email,
          companyId: createInvitationDto.companyId,
          status: InvitationStatus.PENDING,
        },
      });

      if (existingInvitation) {
        throw new ConflictException('Já existe um convite pendente para este email nesta empresa');
      }

      // Verificar se o usuário já está associado à empresa
      const existingUser = await this.prisma.user.findFirst({
        where: {
          email: createInvitationDto.email,
          deletedAt: null,
        },
        include: {
          userCompanyRoles: {
            where: {
              companyId: createInvitationDto.companyId,
              deletedAt: null,
            },
          },
        },
      });

      if (existingUser && existingUser.userCompanyRoles.length > 0) {
        throw new ConflictException('Usuário já está associado a esta empresa');
      }

      // Gerar token único
      const token = this.generateInvitationToken();

      // Definir data de expiração (24 horas por padrão)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      // Criar convite
      const invitation = await this.prisma.companyInvitation.create({
        data: {
          id: uuidv4(),
          token,
          email: createInvitationDto.email,
          companyId: createInvitationDto.companyId,
          roleId: createInvitationDto.roleId,
          status: InvitationStatus.PENDING,
          expiresAt,
          invitedBy,
          message: createInvitationDto.message,
        },
      });

      this.logger.log(`Convite criado para ${createInvitationDto.email} na empresa ${createInvitationDto.companyId}`);

      return this.mapToDto(invitation);
    } catch (error) {
      this.logger.error(`Erro ao criar convite: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Valida um token de convite
   */
  async validateInvitation(token: string): Promise<InvitationValidationDto> {
    try {
      const invitation = await this.prisma.companyInvitation.findFirst({
        where: { token },
        include: {
          company: true,
          role: true,
        },
      });

      if (!invitation) {
        return {
          token,
          isValid: false,
          errorMessage: 'Convite não encontrado',
        };
      }

      if (invitation.status !== InvitationStatus.PENDING) {
        return {
          token,
          isValid: false,
          errorMessage: 'Convite já foi utilizado ou revogado',
        };
      }

      if (new Date() > invitation.expiresAt) {
        // Marcar como expirado
        await this.prisma.companyInvitation.update({
          where: { id: invitation.id },
          data: { status: InvitationStatus.EXPIRED },
        });

        return {
          token,
          isValid: false,
          errorMessage: 'Convite expirado',
        };
      }

      return {
        token,
        isValid: true,
        companyName: invitation.company.name,
        roleName: invitation.role.name,
        email: invitation.email,
        expiresAt: invitation.expiresAt,
      };
    } catch (error) {
      this.logger.error(`Erro ao validar convite: ${error.message}`, error.stack);
      throw new BadRequestException('Erro ao validar convite');
    }
  }

  /**
   * Aceita um convite (associa usuário à empresa)
   */
  async acceptInvitation(acceptDto: AcceptInvitationDto, userId: string): Promise<void> {
    try {
      const invitation = await this.prisma.companyInvitation.findFirst({
        where: { token: acceptDto.token },
        include: {
          company: true,
          role: true,
        },
      });

      if (!invitation) {
        throw new NotFoundException('Convite não encontrado');
      }

      if (invitation.status !== InvitationStatus.PENDING) {
        throw new BadRequestException('Convite já foi utilizado ou revogado');
      }

      if (new Date() > invitation.expiresAt) {
        throw new BadRequestException('Convite expirado');
      }

      // Verificar se o usuário existe e tem o email correto
      const user = await this.prisma.user.findFirst({
        where: {
          id: userId,
          email: invitation.email,
          deletedAt: null,
        },
      });

      if (!user) {
        throw new ForbiddenException('Usuário não autorizado para este convite');
      }

      // Verificar se o usuário já está associado à empresa
      const existingAssociation = await this.prisma.userCompanyRole.findFirst({
        where: {
          userId,
          companyId: invitation.companyId,
          deletedAt: null,
        },
      });

      if (existingAssociation) {
        throw new ConflictException('Usuário já está associado a esta empresa');
      }

      // Usar transação para associar usuário e marcar convite como aceito
      await this.prisma.$transaction(async (tx) => {
        // Criar associação usuário-empresa-papel
        await tx.userCompanyRole.create({
          data: {
            id: uuidv4(),
            userId,
            companyId: invitation.companyId,
            roleId: invitation.roleId,
          },
        });

        // Marcar convite como aceito
        await tx.companyInvitation.update({
          where: { id: invitation.id },
          data: { status: InvitationStatus.ACCEPTED },
        });

        // Atualizar status do usuário se estava sem empresa
        if (user.status === 'no_company') {
          await tx.user.update({
            where: { id: userId },
            data: { status: 'active' },
          });
        }
      });

      this.logger.log(`Convite aceito: usuário ${userId} associado à empresa ${invitation.companyId}`);
    } catch (error) {
      this.logger.error(`Erro ao aceitar convite: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lista convites de uma empresa
   */
  async findByCompany(
    companyId: string,
    userId: string,
    page = 1,
    limit = 10,
  ): Promise<CompanyInvitationListDto> {
    try {
      // Verificar se o usuário tem acesso à empresa
      await this.validateUserCanInvite(userId, companyId);

      const skip = (page - 1) * limit;

      const [invitations, total] = await Promise.all([
        this.prisma.companyInvitation.findMany({
          where: { companyId },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
        }),
        this.prisma.companyInvitation.count({
          where: { companyId },
        }),
      ]);

      return {
        items: invitations.map(this.mapToDto),
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Erro ao listar convites: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Revoga um convite
   */
  async revokeInvitation(invitationId: string, userId: string): Promise<void> {
    try {
      const invitation = await this.prisma.companyInvitation.findFirst({
        where: { id: invitationId },
      });

      if (!invitation) {
        throw new NotFoundException('Convite não encontrado');
      }

      // Verificar se o usuário tem permissão
      await this.validateUserCanInvite(userId, invitation.companyId);

      if (invitation.status !== InvitationStatus.PENDING) {
        throw new BadRequestException('Apenas convites pendentes podem ser revogados');
      }

      await this.prisma.companyInvitation.update({
        where: { id: invitationId },
        data: { status: InvitationStatus.REVOKED },
      });

      this.logger.log(`Convite ${invitationId} revogado por usuário ${userId}`);
    } catch (error) {
      this.logger.error(`Erro ao revogar convite: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Valida se o usuário pode enviar convites para a empresa
   */
  private async validateUserCanInvite(userId: string, companyId: string): Promise<void> {
    const hasAdminAccess = await this.prisma.$queryRaw<{ count: string }[]>`
      SELECT COUNT(*) as count
      FROM user_company_roles ucr
      JOIN roles r ON ucr.role_id = r.id
      WHERE ucr.user_id = ${userId}::uuid
      AND ucr.company_id = ${companyId}::uuid
      AND r.name = 'Administrador'
      AND ucr.deleted_at IS NULL
      AND r.deleted_at IS NULL
    `;

    if (parseInt(hasAdminAccess[0].count, 10) === 0) {
      throw new ForbiddenException('Apenas administradores podem gerenciar convites');
    }
  }

  /**
   * Gera um token único para convite
   */
  private generateInvitationToken(): string {
    return randomBytes(32).toString('hex');
  }

  /**
   * Mapeia entidade para DTO
   */
  private mapToDto(invitation: any): CompanyInvitationDto {
    return {
      id: invitation.id,
      token: invitation.token,
      email: invitation.email,
      companyId: invitation.companyId,
      roleId: invitation.roleId,
      status: invitation.status,
      expiresAt: invitation.expiresAt,
      invitedBy: invitation.invitedBy,
      createdAt: invitation.createdAt,
      updatedAt: invitation.updatedAt,
    };
  }
}
