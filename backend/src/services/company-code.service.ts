import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { v4 as uuidv4 } from 'uuid';
import {
  CreateCompanyCodeDto,
  CompanyCodeDto,
  JoinCompanyByCodeDto,
  UpdateCompanyCodeDto,
  CompanyCodeValidationDto,
  CompanyCodeListDto,
} from '../models/company-code.model';

@Injectable()
export class CompanyCodeService {
  private readonly logger = new Logger(CompanyCodeService.name);

  constructor(private prisma: PrismaService) {}

  /**
   * Cria um novo código de empresa
   */
  async create(
    createCodeDto: CreateCompanyCodeDto,
    createdBy: string,
  ): Promise<CompanyCodeDto> {
    try {
      // Verificar se o usuário tem permissão (deve ser admin da empresa)
      await this.validateUserCanManageCodes(createdBy, createCodeDto.companyId);

      // Verificar se a empresa existe
      const company = await this.prisma.company.findFirst({
        where: {
          id: createCodeDto.companyId,
          deleted_at: null,
        },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // Verificar se o papel existe
      const role = await this.prisma.role.findFirst({
        where: {
          id: createCodeDto.defaultRoleId,
          companyId: createCodeDto.companyId,
          deletedAt: null,
        },
      });

      if (!role) {
        throw new NotFoundException('Papel não encontrado ou não pertence à empresa');
      }

      // Gerar código único
      const code = await this.generateUniqueCode();

      // Processar data de expiração se fornecida
      let expiresAt: Date | null = null;
      if (createCodeDto.expiresAt) {
        expiresAt = new Date(createCodeDto.expiresAt);
        if (expiresAt <= new Date()) {
          throw new BadRequestException('Data de expiração deve ser no futuro');
        }
      }

      // Criar código
      const companyCode = await this.prisma.companyCode.create({
        data: {
          id: uuidv4(),
          code,
          companyId: createCodeDto.companyId,
          defaultRoleId: createCodeDto.defaultRoleId,
          isActive: true,
          expiresAt,
          description: createCodeDto.description,
          usageCount: 0,
          maxUsage: createCodeDto.maxUsage,
          createdBy,
        },
      });

      this.logger.log(`Código de empresa criado: ${code} para empresa ${createCodeDto.companyId}`);

      return this.mapToDto(companyCode);
    } catch (error) {
      this.logger.error(`Erro ao criar código de empresa: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Valida um código de empresa
   */
  async validateCode(code: string): Promise<CompanyCodeValidationDto> {
    try {
      const companyCode = await this.prisma.companyCode.findFirst({
        where: { code },
        include: {
          company: true,
          defaultRole: true,
        },
      });

      if (!companyCode) {
        return {
          code,
          isValid: false,
          errorMessage: 'Código não encontrado',
        };
      }

      if (!companyCode.isActive) {
        return {
          code,
          isValid: false,
          errorMessage: 'Código desativado',
        };
      }

      if (companyCode.expiresAt && new Date() > companyCode.expiresAt) {
        return {
          code,
          isValid: false,
          errorMessage: 'Código expirado',
        };
      }

      if (companyCode.maxUsage && companyCode.usageCount >= companyCode.maxUsage) {
        return {
          code,
          isValid: false,
          errorMessage: 'Código atingiu o limite máximo de usos',
        };
      }

      return {
        code,
        isValid: true,
        companyName: companyCode.company.name,
        roleName: companyCode.defaultRole.name,
      };
    } catch (error) {
      this.logger.error(`Erro ao validar código: ${error.message}`, error.stack);
      throw new BadRequestException('Erro ao validar código');
    }
  }

  /**
   * Associa usuário à empresa usando código
   */
  async joinCompanyByCode(joinDto: JoinCompanyByCodeDto, userId: string): Promise<void> {
    try {
      const companyCode = await this.prisma.companyCode.findFirst({
        where: { code: joinDto.code },
        include: {
          company: true,
          defaultRole: true,
        },
      });

      if (!companyCode) {
        throw new NotFoundException('Código não encontrado');
      }

      if (!companyCode.isActive) {
        throw new BadRequestException('Código desativado');
      }

      if (companyCode.expiresAt && new Date() > companyCode.expiresAt) {
        throw new BadRequestException('Código expirado');
      }

      if (companyCode.maxUsage && companyCode.usageCount >= companyCode.maxUsage) {
        throw new BadRequestException('Código atingiu o limite máximo de usos');
      }

      // Verificar se o usuário existe
      const user = await this.prisma.user.findFirst({
        where: {
          id: userId,
          deletedAt: null,
        },
      });

      if (!user) {
        throw new NotFoundException('Usuário não encontrado');
      }

      // Verificar se o usuário já está associado à empresa
      const existingAssociation = await this.prisma.userCompanyRole.findFirst({
        where: {
          userId,
          companyId: companyCode.companyId,
          deletedAt: null,
        },
      });

      if (existingAssociation) {
        throw new ConflictException('Usuário já está associado a esta empresa');
      }

      // Usar transação para associar usuário e incrementar contador
      await this.prisma.$transaction(async (tx) => {
        // Criar associação usuário-empresa-papel
        await tx.userCompanyRole.create({
          data: {
            id: uuidv4(),
            userId,
            companyId: companyCode.companyId,
            roleId: companyCode.defaultRoleId,
          },
        });

        // Incrementar contador de uso
        await tx.companyCode.update({
          where: { id: companyCode.id },
          data: { usageCount: { increment: 1 } },
        });

        // Atualizar status do usuário se estava sem empresa
        if (user.status === 'sem_empresa') {
          await tx.user.update({
            where: { id: userId },
            data: { status: 'active' },
          });
        }
      });

      this.logger.log(`Usuário ${userId} associado à empresa ${companyCode.companyId} via código ${joinDto.code}`);
    } catch (error) {
      this.logger.error(`Erro ao associar usuário via código: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lista códigos de uma empresa
   */
  async findByCompany(
    companyId: string,
    userId: string,
    page = 1,
    limit = 10,
  ): Promise<CompanyCodeListDto> {
    try {
      // Verificar se o usuário tem acesso à empresa
      await this.validateUserCanManageCodes(userId, companyId);

      const skip = (page - 1) * limit;

      const [codes, total] = await Promise.all([
        this.prisma.companyCode.findMany({
          where: { companyId },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
        }),
        this.prisma.companyCode.count({
          where: { companyId },
        }),
      ]);

      return {
        items: codes.map(this.mapToDto),
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Erro ao listar códigos: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Atualiza um código de empresa
   */
  async update(
    codeId: string,
    updateDto: UpdateCompanyCodeDto,
    userId: string,
  ): Promise<CompanyCodeDto> {
    try {
      const companyCode = await this.prisma.companyCode.findFirst({
        where: { id: codeId },
      });

      if (!companyCode) {
        throw new NotFoundException('Código não encontrado');
      }

      // Verificar se o usuário tem permissão
      await this.validateUserCanManageCodes(userId, companyCode.companyId);

      // Processar data de expiração se fornecida
      let expiresAt: Date | null = companyCode.expiresAt;
      if (updateDto.expiresAt !== undefined) {
        if (updateDto.expiresAt) {
          expiresAt = new Date(updateDto.expiresAt);
          if (expiresAt <= new Date()) {
            throw new BadRequestException('Data de expiração deve ser no futuro');
          }
        } else {
          expiresAt = null;
        }
      }

      const updatedCode = await this.prisma.companyCode.update({
        where: { id: codeId },
        data: {
          isActive: updateDto.isActive ?? companyCode.isActive,
          description: updateDto.description ?? companyCode.description,
          expiresAt,
          maxUsage: updateDto.maxUsage ?? companyCode.maxUsage,
        },
      });

      this.logger.log(`Código ${companyCode.code} atualizado por usuário ${userId}`);

      return this.mapToDto(updatedCode);
    } catch (error) {
      this.logger.error(`Erro ao atualizar código: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Remove um código de empresa
   */
  async remove(codeId: string, userId: string): Promise<void> {
    try {
      const companyCode = await this.prisma.companyCode.findFirst({
        where: { id: codeId },
      });

      if (!companyCode) {
        throw new NotFoundException('Código não encontrado');
      }

      // Verificar se o usuário tem permissão
      await this.validateUserCanManageCodes(userId, companyCode.companyId);

      await this.prisma.companyCode.delete({
        where: { id: codeId },
      });

      this.logger.log(`Código ${companyCode.code} removido por usuário ${userId}`);
    } catch (error) {
      this.logger.error(`Erro ao remover código: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Valida se o usuário pode gerenciar códigos da empresa
   */
  private async validateUserCanManageCodes(userId: string, companyId: string): Promise<void> {
    const hasAdminAccess = await this.prisma.$queryRaw<{ count: string }[]>`
      SELECT COUNT(*) as count
      FROM user_company_roles ucr
      JOIN roles r ON ucr.role_id = r.id
      WHERE ucr.user_id = ${userId}::uuid
      AND ucr.company_id = ${companyId}::uuid
      AND r.name = 'Administrador'
      AND ucr.deleted_at IS NULL
      AND r.deleted_at IS NULL
    `;

    if (parseInt(hasAdminAccess[0].count, 10) === 0) {
      throw new ForbiddenException('Apenas administradores podem gerenciar códigos de empresa');
    }
  }

  /**
   * Gera um código único
   */
  private async generateUniqueCode(): Promise<string> {
    let code: string;
    let isUnique = false;

    while (!isUnique) {
      code = this.generateRandomCode();
      const existing = await this.prisma.companyCode.findFirst({
        where: { code },
      });
      isUnique = !existing;
    }

    return code!;
  }

  /**
   * Gera um código aleatório
   */
  private generateRandomCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Mapeia entidade para DTO
   */
  private mapToDto(companyCode: any): CompanyCodeDto {
    return {
      id: companyCode.id,
      code: companyCode.code,
      companyId: companyCode.companyId,
      defaultRoleId: companyCode.defaultRoleId,
      isActive: companyCode.isActive,
      expiresAt: companyCode.expiresAt,
      description: companyCode.description,
      usageCount: companyCode.usageCount,
      maxUsage: companyCode.maxUsage,
      createdBy: companyCode.createdBy,
      createdAt: companyCode.createdAt,
      updatedAt: companyCode.updatedAt,
    };
  }
}
