import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PrismaService } from '../services/prisma.service';

/**
 * Guard que verifica se o usuário tem uma empresa associada
 * Usado para proteger rotas que requerem que o usuário tenha uma empresa
 */
@Injectable()
export class CompanyRequiredGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private prisma: PrismaService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Verificar se a rota permite usuários sem empresa
    const allowWithoutCompany = this.reflector.get<boolean>(
      'allowWithoutCompany',
      context.getHandler(),
    );

    if (allowWithoutCompany) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.userId) {
      throw new ForbiddenException('Usuário não autenticado');
    }

    // Verificar se o usuário tem pelo menos uma empresa associada
    const userCompanyRole = await this.prisma.userCompanyRole.findFirst({
      where: {
        userId: user.userId,
        deletedAt: null,
      },
    });

    if (!userCompanyRole) {
      throw new ForbiddenException(
        'Acesso negado: você precisa estar associado a uma empresa para acessar esta funcionalidade. ' +
        'Por favor, crie uma empresa ou solicite acesso a uma empresa existente.',
      );
    }

    return true;
  }
}

/**
 * Decorator para permitir acesso sem empresa
 */
export const AllowWithoutCompany = () => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    Reflect.defineMetadata('allowWithoutCompany', true, descriptor.value);
  };
};
