import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './routes/auth.module';
import { UsersModule } from './routes/users.module';
import { HealthModule } from './routes/health.module';
import { CompaniesModule } from './routes/companies.module';
import { PrismaModule } from './prisma/prisma.module';
import { RolesModule } from './routes/roles/roles.module';
import { UserCompanyRolesModule } from './routes/user-company-roles/user-company-roles.module';
import { AddressesModule } from './routes/addresses.module';
import { AccountsPayableModule } from './routes/accounts-payable/accounts-payable.module';
import { AccountsReceivableModule } from './routes/accounts-receivable/accounts-receivable.module';
import { BankAccountsModule } from './routes/bank-accounts/bank-accounts.module';
import { BanksModule } from './routes/banks/banks.module';
import { CategoriesModule } from './routes/categories/categories.module';
import { CustomPeriodsModule } from './routes/custom-periods/custom-periods.module';
import { EntitiesModule } from './routes/entities/entities.module';
import { ProjectsModule } from './routes/projects/projects.module';
import { ZipCodesModule } from './routes/zip-codes/zip-codes.module';
import { TransactionsModule } from './routes/transactions/transactions.module';
import { UtilsModule } from './utils/utils.module';
import { SystemPermissionsModule } from './routes/system-permissions/system-permissions.module';
import { PermissionsModule } from './routes/permissions/permissions.module';
import { RbacModule } from './routes/rbac/rbac.module';
import { CurrenciesModule } from './routes/currencies/currencies.module';
import { PaymentMethodsModule } from './routes/payment-methods/payment-methods.module';
import { RecurrenceTypesModule } from './routes/recurrence-types/recurrence-types.module';
import { AddressTypesModule } from './routes/address-types/address-types.module';
import { ReportsModule } from './routes/reports/reports.module';
import { RecurringSchedulesModule } from './routes/recurring-schedules/recurring-schedules.module';
import { TasksModule } from './tasks/tasks.module';
import { CompanyInvitationsModule } from './routes/company-invitations.module';
import { CompanyCodesModule } from './routes/company-codes.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    PrismaModule,
    UtilsModule,
    AuthModule,
    UsersModule,
    HealthModule,
    CompaniesModule,
    SystemPermissionsModule,
    PermissionsModule,
    RolesModule,
    UserCompanyRolesModule,
    RbacModule,
    AddressesModule,
    AccountsPayableModule,
    AccountsReceivableModule,
    BankAccountsModule,
    BanksModule,
    CategoriesModule,
    EntitiesModule,
    ProjectsModule,
    ZipCodesModule,
    TransactionsModule,
    CustomPeriodsModule,
    CurrenciesModule,
    PaymentMethodsModule,
    RecurrenceTypesModule,
    AddressTypesModule,
    ReportsModule,
    RecurringSchedulesModule,
    TasksModule,
    CompanyInvitationsModule,
    CompanyCodesModule,
  ],
  controllers: [],
  // providers: [PrismaService], // Remover daqui
  // exports: [PrismaService], // Remover daqui
})
export class AppModule {}
