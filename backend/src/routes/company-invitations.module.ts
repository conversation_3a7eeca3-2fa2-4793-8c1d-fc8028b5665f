import { Module } from '@nestjs/common';
import { CompanyInvitationController } from '../controllers/company-invitation.controller';
import { CompanyInvitationService } from '../services/company-invitation.service';
import { PrismaService } from '../services/prisma.service';

@Module({
  controllers: [CompanyInvitationController],
  providers: [CompanyInvitationService, PrismaService],
  exports: [CompanyInvitationService],
})
export class CompanyInvitationsModule {}
