import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CreateRecurringScheduleDto, ReferenceTableEnum } from './dto/create-recurring-schedule.dto';
import { UpdateRecurringScheduleDto } from './dto/update-recurring-schedule.dto';
import { Prisma, RecurringSchedule } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { AuthenticatedUser } from '../../interfaces/authenticated-user.interface';

@Injectable()
export class RecurringSchedulesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Criar um novo agendamento recorrente
   */
  async create(
    createDto: CreateRecurringScheduleDto,
    user: AuthenticatedUser,
  ): Promise<RecurringSchedule> {
    const { companyId } = user;

    try {
      // Validar tipo de recorrência
      const recurrenceType = await this.prisma.recurrenceType.findUnique({
        where: { id: createDto.recurrenceTypeId },
      });

      if (!recurrenceType) {
        throw new BadRequestException('Tipo de recorrência não encontrado');
      }

      // Validar entidade se fornecida
      if (createDto.entityId) {
        const entity = await this.prisma.entity.findFirst({
          where: {
            id: createDto.entityId,
            companyId,
            deletedAt: null,
          },
        });

        if (!entity) {
          throw new BadRequestException('Entidade não encontrada ou não pertence à empresa');
        }
      }

      // Validar categoria se fornecida
      if (createDto.categoryId) {
        const category = await this.prisma.category.findFirst({
          where: {
            id: createDto.categoryId,
            companyId,
            deletedAt: null,
          },
        });

        if (!category) {
          throw new BadRequestException('Categoria não encontrada ou não pertence à empresa');
        }
      }

      // Validar projeto se fornecido
      if (createDto.projectId) {
        const project = await this.prisma.project.findFirst({
          where: {
            id: createDto.projectId,
            companyId,
            deletedAt: null,
          },
        });

        if (!project) {
          throw new BadRequestException('Projeto não encontrado ou não pertence à empresa');
        }
      }

      // Validar conta bancária se fornecida
      if (createDto.bankAccountId) {
        const bankAccount = await this.prisma.bankAccount.findFirst({
          where: {
            id: createDto.bankAccountId,
            companyId,
            deletedAt: null,
          },
        });

        if (!bankAccount) {
          throw new BadRequestException('Conta bancária não encontrada ou não pertence à empresa');
        }
      }

      // Validar moeda
      const currency = await this.prisma.currency.findUnique({
        where: { id: createDto.currencyId },
      });

      if (!currency) {
        throw new BadRequestException('Moeda não encontrada');
      }

      // Validar método de pagamento se fornecido
      if (createDto.paymentMethodId) {
        const paymentMethod = await this.prisma.paymentMethod.findUnique({
          where: { id: createDto.paymentMethodId },
        });

        if (!paymentMethod) {
          throw new BadRequestException('Método de pagamento não encontrado');
        }
      }

      // Validar datas
      const startDate = new Date(createDto.startDate);
      const endDate = createDto.endDate ? new Date(createDto.endDate) : null;

      if (endDate && endDate <= startDate) {
        throw new BadRequestException('A data de término deve ser posterior à data de início');
      }

      // Validar dia do mês ou dia da semana conforme o tipo de recorrência
      const recurrenceTypeName = recurrenceType.name.toLowerCase();
      
      // Para recorrências mensais ou superiores, validar dia do mês
      if (['mensal', 'bimestral', 'trimestral', 'semestral', 'anual'].includes(recurrenceTypeName)) {
        if (!createDto.dayOfMonth) {
          // Se não for fornecido, usar o dia da data de início
          createDto.dayOfMonth = startDate.getDate();
        }
      }
      
      // Para recorrências semanais, validar dia da semana
      if (['semanal', 'quinzenal'].includes(recurrenceTypeName)) {
        if (!createDto.dayOfWeek) {
          // Se não for fornecido, usar o dia da semana da data de início
          createDto.dayOfWeek = startDate.getDay();
        }
      }

      // Calcular a primeira data de geração (nextGenerationDate)
      // Para a primeira vez, será a própria data de início
      const nextGenerationDate = startDate;

      // Preparar dados para criação
      const data: Prisma.RecurringScheduleCreateInput = {
        description: createDto.description,
        referenceTable: createDto.referenceTable,
        referenceId: createDto.referenceId,
        dayOfMonth: createDto.dayOfMonth,
        dayOfWeek: createDto.dayOfWeek,
        startDate,
        endDate,
        amount: new Decimal(createDto.amount),
        active: true,
        nextGenerationDate,
        company: { connect: { id: companyId } },
        recurrenceType: { connect: { id: createDto.recurrenceTypeId } },
        currency: { connect: { id: createDto.currencyId } },
      };

      // Adicionar relações opcionais
      if (createDto.entityId) {
        data.entity = { connect: { id: createDto.entityId } };
      }
      if (createDto.categoryId) {
        data.category = { connect: { id: createDto.categoryId } };
      }
      if (createDto.projectId) {
        data.project = { connect: { id: createDto.projectId } };
      }
      if (createDto.paymentMethodId) {
        data.paymentMethod = { connect: { id: createDto.paymentMethodId } };
      }
      if (createDto.bankAccountId) {
        data.bankAccount = { connect: { id: createDto.bankAccountId } };
      }

      // Criar o agendamento recorrente
      return await this.prisma.recurringSchedule.create({ data });
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      console.error('Erro ao criar agendamento recorrente:', error);
      throw new InternalServerErrorException('Não foi possível criar o agendamento recorrente');
    }
  }

  /**
   * Listar todos os agendamentos recorrentes com filtros e paginação
   */
  async findAll(
    user: AuthenticatedUser,
    {
      page = 1,
      limit = 10,
      active,
      referenceTable,
      entityId,
    }: {
      page?: number;
      limit?: number;
      active?: boolean;
      referenceTable?: ReferenceTableEnum;
      entityId?: string;
    } = {},
  ): Promise<{
    data: RecurringSchedule[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { companyId } = user;
    const skip = (page - 1) * limit;

    // Construir filtros
    const where: Prisma.RecurringScheduleWhereInput = {
      companyId,
      deletedAt: null,
    };

    // Adicionar filtros opcionais
    if (active !== undefined) {
      where.active = active;
    }

    if (referenceTable) {
      where.referenceTable = referenceTable;
    }

    if (entityId) {
      where.entityId = entityId;
    }

    try {
      // Executar consulta com contagem total
      const [data, total] = await this.prisma.$transaction([
        this.prisma.recurringSchedule.findMany({
          where,
          skip,
          take: limit,
          orderBy: { nextGenerationDate: 'asc' },
          include: {
            recurrenceType: true,
            entity: true,
            category: true,
            project: true,
            currency: true,
            paymentMethod: true,
            bankAccount: true,
          },
        }),
        this.prisma.recurringSchedule.count({ where }),
      ]);

      return { data, total, page, limit };
    } catch (error) {
      console.error('Erro ao listar agendamentos recorrentes:', error);
      throw new InternalServerErrorException('Não foi possível listar os agendamentos recorrentes');
    }
  }

  /**
   * Buscar um agendamento recorrente pelo ID
   */
  async findOne(id: string, user: AuthenticatedUser): Promise<RecurringSchedule> {
    const { companyId } = user;

    try {
      const recurringSchedule = await this.prisma.recurringSchedule.findFirst({
        where: {
          id,
          companyId,
          deletedAt: null,
        },
        include: {
          recurrenceType: true,
          entity: true,
          category: true,
          project: true,
          currency: true,
          paymentMethod: true,
          bankAccount: true,
        },
      });

      if (!recurringSchedule) {
        throw new NotFoundException('Agendamento recorrente não encontrado');
      }

      return recurringSchedule;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Erro ao buscar agendamento recorrente:', error);
      throw new InternalServerErrorException('Não foi possível buscar o agendamento recorrente');
    }
  }

  /**
   * Atualizar um agendamento recorrente
   */
  async update(
    id: string,
    updateDto: UpdateRecurringScheduleDto,
    user: AuthenticatedUser,
  ): Promise<RecurringSchedule> {
    // Verificar se o agendamento existe e pertence à empresa
    await this.findOne(id, user);

    try {
      // Preparar dados para atualização
      const updateData: Prisma.RecurringScheduleUpdateInput = { ...updateDto };

      // Converter valores numéricos
      if (updateDto.amount !== undefined) {
        updateData.amount = new Decimal(updateDto.amount);
      }

      // Converter datas
      if (updateDto.startDate) {
        updateData.startDate = new Date(updateDto.startDate);
      }

      if (updateDto.endDate) {
        updateData.endDate = new Date(updateDto.endDate);
      }

      // Verificar se é necessário recalcular nextGenerationDate
      // Se startDate ou recurrenceTypeId mudaram, precisamos recalcular
      if (updateDto.startDate || updateDto.recurrenceTypeId) {
        // Aqui precisaríamos chamar a função do banco de dados para calcular a próxima data
        // Como não temos acesso direto à função, vamos usar uma query raw
        
        // Primeiro, atualizamos os dados básicos
        const updatedSchedule = await this.prisma.recurringSchedule.update({
          where: { id },
          data: updateData,
        });
        
        // Depois, chamamos a função do banco para recalcular a próxima data
        await this.prisma.$executeRawUnsafe(
          `UPDATE recurring_schedules 
           SET next_generation_date = calculate_next_recurring_date(id) 
           WHERE id = '${id}'`
        );
        
        // Buscar o registro atualizado
        return this.findOne(id, user);
      }

      // Caso não precise recalcular, apenas atualizar normalmente
      return await this.prisma.recurringSchedule.update({
        where: { id },
        data: updateData,
        include: {
          recurrenceType: true,
          entity: true,
          category: true,
          project: true,
          currency: true,
          paymentMethod: true,
          bankAccount: true,
        },
      });
    } catch (error) {
      console.error('Erro ao atualizar agendamento recorrente:', error);
      throw new InternalServerErrorException('Não foi possível atualizar o agendamento recorrente');
    }
  }

  /**
   * Remover um agendamento recorrente (soft delete)
   */
  async remove(id: string, user: AuthenticatedUser): Promise<void> {
    // Verificar se o agendamento existe e pertence à empresa
    await this.findOne(id, user);

    try {
      // Realizar soft delete
      await this.prisma.recurringSchedule.update({
        where: { id },
        data: {
          active: false,
          deletedAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Erro ao remover agendamento recorrente:', error);
      throw new InternalServerErrorException('Não foi possível remover o agendamento recorrente');
    }
  }

  /**
   * Gerar contas agendadas que estão no prazo
   * Este método será chamado pelo agendador (cron job)
   */
  async generateScheduledAccounts(): Promise<number> {
    try {
      // Chamar a função do banco de dados para gerar as contas
      const result = await this.prisma.$executeRawUnsafe('SELECT generate_recurring_accounts()') as unknown as any[];

      // A função retorna o número de contas criadas
      const accountsCreated = result[0]?.generate_recurring_accounts || 0;
      console.log(`Contas recorrentes geradas: ${accountsCreated}`);

      return accountsCreated;
    } catch (error) {
      console.error('Erro ao gerar contas recorrentes:', error);
      throw new InternalServerErrorException('Não foi possível gerar as contas recorrentes');
    }
  }
}
