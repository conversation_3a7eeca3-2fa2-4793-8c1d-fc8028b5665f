import { Module } from '@nestjs/common';
import { CompanyCodeController } from '../controllers/company-code.controller';
import { CompanyCodeService } from '../services/company-code.service';
import { PrismaService } from '../services/prisma.service';

@Module({
  controllers: [CompanyCodeController],
  providers: [CompanyCodeService, PrismaService],
  exports: [CompanyCodeService],
})
export class CompanyCodesModule {}
