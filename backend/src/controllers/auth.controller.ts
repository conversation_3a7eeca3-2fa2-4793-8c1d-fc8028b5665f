import {
  Controller,
  Post,
  Get,
  Body,
  UnauthorizedException,
  UseGuards,
  Req,
  HttpCode,
} from '@nestjs/common';
import { AuthService } from '../services/auth.service';
import { UsersService } from '../services/users.service';
import { CreateUserDto, LoginDto, UserDto } from '../models/user.model';
import { AuthTokens, RefreshTokenDto } from '../models/auth.model';
import { JwtAuthGuard } from '../middlewares/jwt-auth.guard';
import { Request } from 'express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private usersService: UsersService,
  ) {}

  @Post('register')
  @ApiOperation({ summary: 'Registrar novo usuário' })
  @ApiResponse({
    status: 201,
    description: 'Usuário registrado com sucesso',
    type: UserDto,
  })
  @ApiResponse({ status: 400, description: 'Dados de entrada inválidos' })
  @ApiResponse({ status: 409, description: 'Email já existe' })
  async register(@Body() createUserDto: CreateUserDto): Promise<UserDto> {
    return this.usersService.create(createUserDto);
  }

  @Post('login')
  @HttpCode(200)
  @ApiOperation({ summary: 'Autenticar usuário' })
  @ApiBody({ type: LoginDto })
  @ApiResponse({
    status: 200,
    description: 'Autenticação bem-sucedida',
    type: AuthTokens,
  })
  @ApiResponse({ status: 401, description: 'Credenciais inválidas' })
  async login(@Body() loginDto: LoginDto): Promise<AuthTokens> {
    return this.authService.login(loginDto);
  }

  @Post('refresh-token')
  @HttpCode(200)
  @ApiOperation({ summary: 'Renovar token de acesso' })
  @ApiBody({ type: RefreshTokenDto })
  @ApiResponse({
    status: 200,
    description: 'Token renovado com sucesso',
    type: AuthTokens,
  })
  @ApiResponse({
    status: 401,
    description: 'Refresh token inválido ou expirado',
  })
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
  ): Promise<AuthTokens> {
    return this.authService.refreshToken(refreshTokenDto);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(204)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Encerrar sessão' })
  @ApiBody({ type: RefreshTokenDto })
  @ApiResponse({ status: 204, description: 'Sessão encerrada com sucesso' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async logout(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.logout(refreshTokenDto.refreshToken);
  }

  @Post('change-password')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Alterar senha do usuário autenticado' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        currentPassword: { type: 'string', description: 'Senha atual' },
        newPassword: { type: 'string', description: 'Nova senha' },
      },
      required: ['currentPassword', 'newPassword'],
    },
  })
  @ApiResponse({ status: 200, description: 'Senha alterada com sucesso' })
  @ApiResponse({ status: 401, description: 'Senha atual incorreta' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async changePassword(
    @Req() req: Request,
    @Body() body: { currentPassword: string; newPassword: string },
  ): Promise<{ success: boolean }> {
    const user = req.user as { userId: string };
    await this.authService.changePassword(
      user.userId,
      body.currentPassword,
      body.newPassword,
    );
    return { success: true };
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obter informações do usuário autenticado' })
  @ApiResponse({
    status: 200,
    description: 'Informações do usuário',
    schema: {
      type: 'object',
      properties: {
        user: { $ref: '#/components/schemas/UserDto' },
        hasCompany: { type: 'boolean', description: 'Se o usuário tem empresa associada' },
        companies: {
          type: 'array',
          items: { $ref: '#/components/schemas/CompanyDto' },
          description: 'Lista de empresas do usuário',
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async getMe(@Req() req: Request): Promise<{
    user: UserDto;
    hasCompany: boolean;
    companies: any[];
  }> {
    const user = req.user as { userId: string };
    const userInfo = await this.usersService.findById(user.userId);

    // Verificar se o usuário tem empresas associadas
    const userCompanyRoles = await this.usersService.getUserCompanies(user.userId);

    return {
      user: userInfo,
      hasCompany: userCompanyRoles.length > 0,
      companies: userCompanyRoles,
    };
  }
}
