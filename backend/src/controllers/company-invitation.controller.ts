import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  Delete,
} from '@nestjs/common';
import { CompanyInvitationService } from '../services/company-invitation.service';
import { JwtAuthGuard } from '../middlewares/jwt-auth.guard';
import { Request } from 'express';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import {
  CreateCompanyInvitationDto,
  CompanyInvitationDto,
  AcceptInvitationDto,
  InvitationValidationDto,
  CompanyInvitationListDto,
} from '../models/company-invitation.model';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    userId: string;
    email: string;
    companyId?: string;
  };
}

@ApiTags('company-invitations')
@Controller('company-invitations')
export class CompanyInvitationController {
  constructor(
    private readonly companyInvitationService: CompanyInvitationService,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar convite para empresa' })
  @ApiBody({ type: CreateCompanyInvitationDto })
  @ApiResponse({
    status: 201,
    description: 'Convite criado com sucesso',
    type: CompanyInvitationDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Sem permissão para convidar' })
  @ApiResponse({ status: 404, description: 'Empresa ou papel não encontrado' })
  @ApiResponse({ status: 409, description: 'Convite já existe ou usuário já associado' })
  async create(
    @Body() createInvitationDto: CreateCompanyInvitationDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<CompanyInvitationDto> {
    return this.companyInvitationService.create(createInvitationDto, req.user.userId);
  }

  @Get('validate/:token')
  @ApiOperation({ summary: 'Validar token de convite' })
  @ApiParam({ name: 'token', description: 'Token do convite' })
  @ApiResponse({
    status: 200,
    description: 'Validação do convite',
    type: InvitationValidationDto,
  })
  async validateInvitation(
    @Param('token') token: string,
  ): Promise<InvitationValidationDto> {
    return this.companyInvitationService.validateInvitation(token);
  }

  @Post('accept')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Aceitar convite de empresa' })
  @ApiBody({ type: AcceptInvitationDto })
  @ApiResponse({
    status: 200,
    description: 'Convite aceito com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Convite inválido ou expirado' })
  @ApiResponse({ status: 403, description: 'Usuário não autorizado para este convite' })
  @ApiResponse({ status: 404, description: 'Convite não encontrado' })
  @ApiResponse({ status: 409, description: 'Usuário já associado à empresa' })
  async acceptInvitation(
    @Body() acceptDto: AcceptInvitationDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<{ message: string }> {
    await this.companyInvitationService.acceptInvitation(acceptDto, req.user.userId);
    return { message: 'Convite aceito com sucesso' };
  }

  @Get('company/:companyId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar convites de uma empresa' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de convites',
    type: CompanyInvitationListDto,
  })
  @ApiResponse({ status: 403, description: 'Sem permissão para visualizar convites' })
  @ApiResponse({ status: 404, description: 'Empresa não encontrada' })
  async findByCompany(
    @Param('companyId') companyId: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Req() req: AuthenticatedRequest,
  ): Promise<CompanyInvitationListDto> {
    return this.companyInvitationService.findByCompany(
      companyId,
      req.user.userId,
      page,
      limit,
    );
  }

  @Delete(':invitationId')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Revogar convite' })
  @ApiParam({ name: 'invitationId', description: 'ID do convite' })
  @ApiResponse({
    status: 200,
    description: 'Convite revogado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Convite não pode ser revogado' })
  @ApiResponse({ status: 403, description: 'Sem permissão para revogar convite' })
  @ApiResponse({ status: 404, description: 'Convite não encontrado' })
  async revokeInvitation(
    @Param('invitationId') invitationId: string,
    @Req() req: AuthenticatedRequest,
  ): Promise<{ message: string }> {
    await this.companyInvitationService.revokeInvitation(invitationId, req.user.userId);
    return { message: 'Convite revogado com sucesso' };
  }
}
