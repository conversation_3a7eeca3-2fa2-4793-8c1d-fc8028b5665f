import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { CompaniesService } from '../services/companies.service';
import { JwtAuthGuard } from '../middlewares/jwt-auth.guard';
import { AllowWithoutCompany } from '../middlewares/company-required.guard';
import { Request } from 'express';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import {
  CompanyDto,
  CreateCompanyDto,
  UpdateCompanyDto,
  CompanySettingsDto,
} from '../models/company.model';

@ApiTags('companies')
@Controller('companies')
export class CompaniesController {
  constructor(private companiesService: CompaniesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @AllowWithoutCompany()
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar uma nova empresa' })
  @ApiBody({ type: CreateCompanyDto })
  @ApiResponse({
    status: 201,
    description: 'Empresa criada com sucesso',
    type: CompanyDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 409, description: 'CNPJ já existe' })
  async create(
    @Body() createCompanyDto: CreateCompanyDto,
    @Req() req: Request,
  ): Promise<CompanyDto> {
    const userId = req.user?.['userId'];
    const company = await this.companiesService.create(createCompanyDto, userId);
    
    // Garantir que addressId e logo estejam explicitamente incluídos na resposta
    return {
      ...company,
      addressId: company.addressId,
      logo: company.logo
    };
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todas as empresas do usuário' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiResponse({ status: 200, description: 'Lista de empresas' })
  async findAll(
    @Req() req: Request,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    const userId = req.user?.['userId'];
    const result = await this.companiesService.findAll(page, limit, userId);
    
    // Garantir que addressId e logo estejam explicitamente incluídos em cada item
    if (result && result.data) {
      result.data = result.data.map(company => ({
        ...company,
        addressId: company.addressId || '',
        logo: company.logo || ''
      }));
    }
    
    return result;
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar uma empresa pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da empresa' })
  @ApiResponse({
    status: 200,
    description: 'Empresa encontrada',
    type: CompanyDto,
  })
  @ApiResponse({ status: 404, description: 'Empresa não encontrada' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  async findOne(
    @Param('id') id: string,
    @Req() req: Request,
  ): Promise<CompanyDto> {
    const userId = req.user?.['userId'];
    const company = await this.companiesService.findOne(id, userId);
    
    // Garantir que addressId e logo estejam explicitamente incluídos na resposta
    return {
      ...company,
      addressId: company.addressId,
      logo: company.logo
    };
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar uma empresa' })
  @ApiParam({ name: 'id', description: 'ID da empresa' })
  @ApiBody({ type: UpdateCompanyDto })
  @ApiResponse({
    status: 200,
    description: 'Empresa atualizada com sucesso',
    type: CompanyDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Empresa não encontrada' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  async update(
    @Param('id') id: string,
    @Body() updateCompanyDto: UpdateCompanyDto,
    @Req() req: Request,
  ): Promise<CompanyDto> {
    const userId = req.user?.['userId'];
    const company = await this.companiesService.update(id, updateCompanyDto, userId);
    
    // Garantir que addressId e logo estejam explicitamente incluídos na resposta
    return {
      ...company,
      addressId: company.addressId,
      logo: company.logo
    };
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover uma empresa' })
  @ApiParam({ name: 'id', description: 'ID da empresa' })
  @ApiResponse({ status: 204, description: 'Empresa removida com sucesso' })
  @ApiResponse({ status: 404, description: 'Empresa não encontrada' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  async remove(@Param('id') id: string, @Req() req: Request): Promise<void> {
    const userId = req.user?.['userId'];
    return this.companiesService.remove(id, userId);
  }

  @Get(':id/settings')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Obter configurações da empresa' })
  @ApiParam({ name: 'id', description: 'ID da empresa' })
  @ApiResponse({
    status: 200,
    description: 'Configurações da empresa',
    type: CompanySettingsDto,
  })
  @ApiResponse({ status: 404, description: 'Empresa não encontrada' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  async getSettings(
    @Param('id') id: string,
    @Req() req: Request,
  ): Promise<CompanySettingsDto> {
    const userId = req.user?.['userId'];
    return this.companiesService.getSettings(id, userId);
  }
}
