#!/bin/bash

echo "🔍 Verificando implementação do sistema de onboarding..."

# Verificar se os containers estão rodando
if ! docker compose -f docker-compose.dev.yaml ps | grep -q "Up"; then
    echo "❌ Containers não estão rodando. Execute './dev.sh start' primeiro."
    exit 1
fi

echo ""
echo "📊 Verificando estrutura do banco de dados..."

# Verificar se as tabelas foram criadas
echo "🔍 Verificando tabelas..."
tables_check=$(docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma db execute --stdin" <<< "
SELECT 
    table_name,
    CASE 
        WHEN table_name = 'company_invitations' THEN '✅'
        WHEN table_name = 'company_codes' THEN '✅'
        ELSE '❓'
    END as status
FROM information_schema.tables 
WHERE table_name IN ('company_invitations', 'company_codes', 'users', 'companies')
ORDER BY table_name;
" 2>/dev/null)

echo "$tables_check"

# Verificar se o enum UserStatus foi atualizado
echo ""
echo "🔍 Verificando enum UserStatus..."
enum_check=$(docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma db execute --stdin" <<< "
SELECT enumlabel as status_values 
FROM pg_enum e 
JOIN pg_type t ON e.enumtypid = t.oid 
WHERE t.typname = 'UserStatus'
ORDER BY e.enumsortorder;
" 2>/dev/null)

if echo "$enum_check" | grep -q "no_company"; then
    echo "✅ Status 'no_company' encontrado no enum UserStatus"
else
    echo "❌ Status 'no_company' NÃO encontrado no enum UserStatus"
fi

echo ""
echo "🔍 Verificando colunas das novas tabelas..."

# Verificar colunas da tabela company_invitations
echo "📋 Tabela company_invitations:"
docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma db execute --stdin" <<< "
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'company_invitations'
ORDER BY ordinal_position;
" 2>/dev/null | head -10

echo ""
echo "📋 Tabela company_codes:"
docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma db execute --stdin" <<< "
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'company_codes'
ORDER BY ordinal_position;
" 2>/dev/null | head -10

echo ""
echo "🔍 Verificando endpoints da API..."

# Verificar se o backend está respondendo
backend_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/health 2>/dev/null || echo "000")

if [ "$backend_health" = "200" ]; then
    echo "✅ Backend está respondendo (HTTP 200)"
else
    echo "⚠️  Backend pode não estar pronto (HTTP $backend_health)"
fi

# Verificar se o frontend está respondendo
frontend_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001 2>/dev/null || echo "000")

if [ "$frontend_health" = "200" ]; then
    echo "✅ Frontend está respondendo (HTTP 200)"
else
    echo "⚠️  Frontend pode não estar pronto (HTTP $frontend_health)"
fi

echo ""
echo "📁 Verificando arquivos do sistema..."

# Verificar se os arquivos foram criados
files_to_check=(
    "backend/src/models/company-invitation.model.ts"
    "backend/src/models/company-code.model.ts"
    "backend/src/services/company-invitation.service.ts"
    "backend/src/services/company-code.service.ts"
    "backend/src/controllers/company-invitation.controller.ts"
    "backend/src/controllers/company-code.controller.ts"
    "frontend/src/pages/Onboarding.tsx"
    "frontend/src/components/onboarding/CreateCompanyForm.tsx"
    "frontend/src/components/onboarding/JoinCompanyForm.tsx"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (não encontrado)"
    fi
done

echo ""
echo "🎯 Resumo da Verificação:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Contar verificações
total_files=${#files_to_check[@]}
existing_files=0
for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        ((existing_files++))
    fi
done

echo "📁 Arquivos: $existing_files/$total_files criados"
echo "🗄️  Backend: $([ "$backend_health" = "200" ] && echo "✅ Online" || echo "⚠️  Verificar")"
echo "🌐 Frontend: $([ "$frontend_health" = "200" ] && echo "✅ Online" || echo "⚠️  Verificar")"

if [ "$existing_files" -eq "$total_files" ] && [ "$backend_health" = "200" ] && [ "$frontend_health" = "200" ]; then
    echo ""
    echo "🎉 Sistema de onboarding está pronto para uso!"
    echo ""
    echo "📋 Próximos passos para testar:"
    echo "1. Acesse: http://localhost:3001"
    echo "2. Clique em 'Registrar'"
    echo "3. Crie uma nova conta"
    echo "4. Verifique o redirecionamento para /onboarding"
    echo "5. Teste a criação de empresa"
else
    echo ""
    echo "⚠️  Algumas verificações falharam. Verifique os logs:"
    echo "./dev.sh logs:backend"
    echo "./dev.sh logs:frontend"
fi
