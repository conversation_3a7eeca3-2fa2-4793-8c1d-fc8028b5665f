#!/bin/bash

echo "🚀 Aplicando schema do sistema de onboarding..."

# Verificar se o Docker está rodando
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker não está rodando. Por favor, inicie o Docker primeiro."
    exit 1
fi

# Verificar se o arquivo docker-compose.dev.yaml existe
if [ ! -f "docker-compose.dev.yaml" ]; then
    echo "❌ Arquivo docker-compose.dev.yaml não encontrado."
    echo "Certifique-se de estar no diretório raiz do projeto."
    exit 1
fi

# Verificar se os containers estão rodando
echo "🔍 Verificando status dos containers..."
if ! docker compose -f docker-compose.dev.yaml ps | grep -q "Up"; then
    echo "⚠️  Containers não estão rodando. Use './dev.sh start' para iniciar o ambiente."
    exit 1
fi

echo "🔄 Aplicando schema atualizado ao banco de dados..."

# Usar db push para aplicar as mudanças do schema diretamente
docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma db push"

if [ $? -eq 0 ]; then
    echo "✅ Schema aplicado com sucesso!"
    
    # Gerar cliente Prisma
    echo "🔄 Gerando cliente Prisma..."
    docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma generate"
    
    # Reiniciar backend
    echo "🔄 Reiniciando backend..."
    docker compose -f docker-compose.dev.yaml restart backend
    
    echo "✅ Sistema de onboarding configurado com sucesso!"
    
else
    echo "❌ Erro ao aplicar schema."
    echo "💡 Tente resetar o banco com: ./dev.sh db:reset"
    exit 1
fi

echo ""
echo "🎉 Sistema de convites e códigos de empresa está pronto!"
echo ""
echo "📋 Próximos passos:"
echo "1. Acesse o frontend: http://localhost:3001"
echo "2. Registre um novo usuário"
echo "3. Teste o fluxo de onboarding"
echo ""
echo "🔧 Para verificar logs:"
echo "./dev.sh logs:backend"
