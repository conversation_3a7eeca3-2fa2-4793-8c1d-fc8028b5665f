#!/bin/bash

echo "🐳 Configurando ambiente de desenvolvimento FluxoMax..."

# Verificar se o Docker está rodando
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker não está rodando. Por favor, inicie o Docker primeiro."
    exit 1
fi

# Verificar se o docker-compose.yml existe
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Arquivo docker-compose.yml não encontrado."
    echo "Certifique-se de estar no diretório raiz do projeto."
    exit 1
fi

# Parar containers existentes se estiverem rodando
echo "🛑 Parando containers existentes..."
docker-compose down

# Construir imagens
echo "🔨 Construindo imagens Docker..."
docker-compose build

# Iniciar containers
echo "🚀 Iniciando containers..."
docker-compose up -d

# Aguardar containers iniciarem
echo "⏳ Aguardando containers iniciarem..."
sleep 15

# Verificar se o banco está pronto
echo "🔍 Verificando conexão com banco de dados..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker-compose exec backend npx prisma db push > /dev/null 2>&1; then
        echo "✅ Banco de dados conectado!"
        break
    else
        echo "⏳ Tentativa $attempt/$max_attempts - Aguardando banco..."
        sleep 2
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ Não foi possível conectar ao banco de dados após $max_attempts tentativas."
    echo "Verifique os logs: docker-compose logs database"
    exit 1
fi

# Executar migrations
echo "🔄 Executando migrations..."
docker-compose exec backend npx prisma migrate deploy

# Gerar cliente Prisma
echo "🔄 Gerando cliente Prisma..."
docker-compose exec backend npx prisma generate

# Executar seed se existir
if docker-compose exec backend test -f prisma/seed.ts; then
    echo "🌱 Executando seed do banco..."
    docker-compose exec backend npx prisma db seed
fi

# Reiniciar backend para aplicar mudanças
echo "🔄 Reiniciando backend..."
docker-compose restart backend

# Aguardar backend reiniciar
sleep 5

# Verificar status dos containers
echo "🏥 Verificando status dos containers..."
docker-compose ps

# Verificar se os serviços estão respondendo
echo "🔍 Verificando saúde dos serviços..."

# Verificar backend
echo "Testando backend..."
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    echo "✅ Backend está respondendo"
else
    echo "⚠️  Backend pode não estar pronto ainda"
fi

# Verificar frontend
echo "Testando frontend..."
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend está respondendo"
else
    echo "⚠️  Frontend pode não estar pronto ainda"
fi

echo ""
echo "🎉 Ambiente de desenvolvimento configurado!"
echo ""
echo "📋 Informações dos serviços:"
echo "🌐 Frontend: http://localhost:3000"
echo "🔧 Backend:  http://localhost:3001"
echo "🗄️  Database: localhost:5432"
echo ""
echo "🔧 Comandos úteis:"
echo "- Ver todos os logs:        docker-compose logs -f"
echo "- Ver logs do backend:      docker-compose logs -f backend"
echo "- Ver logs do frontend:     docker-compose logs -f frontend"
echo "- Ver logs do banco:        docker-compose logs -f database"
echo "- Parar containers:         docker-compose down"
echo "- Reiniciar containers:     docker-compose restart"
echo "- Reconstruir containers:   docker-compose up --build -d"
echo ""
echo "🧪 Para testar o sistema de onboarding:"
echo "1. Acesse http://localhost:3000"
echo "2. Registre um novo usuário"
echo "3. Verifique o redirecionamento para /onboarding"
echo "4. Teste a criação de empresa ou associação via código"
