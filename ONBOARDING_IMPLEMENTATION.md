# Sistema de Onboarding e Associação de Usuários e Empresas

## Visão Geral

Este documento descreve a implementação completa do sistema de onboarding e associação de usuários e empresas no FluxoMax, incluindo:

- Status de usuário "sem empresa"
- Sistema de convites por email
- Códigos de empresa para associação
- Fluxo de onboarding no frontend
- Controle de acesso baseado em empresa

## Arquitetura Implementada

### Backend

#### 1. Modelos de Dados

**Novos Status de Usuário:**
- `no_company`: Usuário registrado mas sem empresa associada
- `active`: Usuário ativo com empresa
- `inactive`: Usuário inativo
- `pending`: Usuário pendente de ativação

**Tabela CompanyInvitation:**
```sql
- id: UUID (PK)
- token: String único
- email: String
- companyId: UUID (FK)
- roleId: UUID (FK)
- status: enum (pending, accepted, expired, revoked)
- expiresAt: DateTime
- invitedBy: UUID (FK)
- message: Text opcional
```

**Tabela CompanyCode:**
```sql
- id: UUID (PK)
- code: String único (8 caracteres)
- companyId: UUID (FK)
- defaultRoleId: UUID (FK)
- isActive: Boolean
- expiresAt: DateTime opcional
- description: Text opcional
- usageCount: Integer
- maxUsage: Integer opcional
- createdBy: UUID (FK)
```

#### 2. Serviços

**CompanyInvitationService:**
- `create()`: Criar convite por email
- `validateInvitation()`: Validar token de convite
- `acceptInvitation()`: Aceitar convite e associar usuário
- `findByCompany()`: Listar convites da empresa
- `revokeInvitation()`: Revogar convite pendente

**CompanyCodeService:**
- `create()`: Criar código de empresa
- `validateCode()`: Validar código
- `joinCompanyByCode()`: Associar usuário via código
- `findByCompany()`: Listar códigos da empresa
- `update()`: Atualizar código
- `remove()`: Remover código

#### 3. Controllers e Rotas

**CompanyInvitationController:**
- `POST /company-invitations`: Criar convite
- `GET /company-invitations/validate/:token`: Validar convite
- `POST /company-invitations/accept`: Aceitar convite
- `GET /company-invitations/company/:companyId`: Listar convites
- `DELETE /company-invitations/:id`: Revogar convite

**CompanyCodeController:**
- `POST /company-codes`: Criar código
- `GET /company-codes/validate/:code`: Validar código
- `POST /company-codes/join`: Associar via código
- `GET /company-codes/company/:companyId`: Listar códigos
- `PUT /company-codes/:id`: Atualizar código
- `DELETE /company-codes/:id`: Remover código

**AuthController (atualizado):**
- `GET /auth/me`: Obter status do usuário e empresas

#### 4. Middleware de Segurança

**CompanyRequiredGuard:**
- Verifica se usuário tem empresa associada
- Bloqueia acesso a funcionalidades financeiras
- Decorator `@AllowWithoutCompany()` para exceções

### Frontend

#### 1. Páginas e Componentes

**Página Onboarding (`/onboarding`):**
- Tela de boas-vindas com opções
- Formulário de criação de empresa
- Formulário de associação via código/convite
- Tela de ajuda

**Componentes:**
- `CreateCompanyForm`: Formulário de criação de empresa
- `JoinCompanyForm`: Formulário de associação (tabs para código/convite)

#### 2. Serviços de API

**companyInvitationService:**
- Métodos para gerenciar convites
- Validação e aceitação de convites

**companyCodeService:**
- Métodos para gerenciar códigos
- Validação e associação via código

**authService (atualizado):**
- `getUserStatus()`: Obter status completo do usuário

#### 3. Roteamento e Proteção

**ProtectedRoute (atualizado):**
- Redireciona usuários sem empresa para `/onboarding`
- Mantém proteção de rotas autenticadas

**AuthContext (atualizado):**
- Verifica status do usuário no login
- Gerencia estado de usuários sem empresa

## Fluxos de Uso

### 1. Registro de Novo Usuário

1. Usuário se registra no sistema
2. Status inicial: `no_company`
3. Redirecionamento automático para `/onboarding`
4. Usuário escolhe entre criar empresa ou associar-se

### 2. Criação de Nova Empresa

1. Usuário preenche dados da empresa
2. Sistema cria empresa e associa usuário como admin
3. Status atualizado para `active`
4. Redirecionamento para dashboard

### 3. Associação via Código

1. Admin gera código de empresa
2. Usuário insere código no onboarding
3. Sistema valida código e associa usuário
4. Status atualizado para `active`

### 4. Associação via Convite

1. Admin envia convite por email
2. Usuário acessa link ou insere token
3. Sistema valida convite e associa usuário
4. Status atualizado para `active`

## Segurança e Controle de Acesso

### 1. Validações

- Convites expiram em 24 horas
- Códigos podem ter limite de uso
- Tokens únicos e seguros
- Validação de permissões de admin

### 2. Auditoria

- Log de criação de convites
- Log de aceitação/revogação
- Rastreamento de uso de códigos
- Eventos de associação

### 3. Restrições

- Usuários sem empresa bloqueados de funcionalidades financeiras
- Apenas admins podem gerenciar convites/códigos
- Validação de empresa ativa

## Instalação e Configuração

### 1. Iniciar Ambiente de Desenvolvimento

```bash
# Iniciar containers Docker
./dev.sh start
```

### 2. Aplicar Schema do Sistema de Onboarding

**Opção A - Aplicação Direta (Recomendado):**
```bash
# Aplica o schema diretamente ao banco (mais simples)
./apply-onboarding-schema.sh
```

**Opção B - Migrations Tradicionais:**
```bash
# Executa migrations específicas para convites e códigos
./run-migrations.sh
```

> **Nota**: Se você encontrar erros de drift no schema, use a Opção A que resolve automaticamente.

### 3. Verificar Serviços

```bash
# Verificar logs dos serviços
./dev.sh logs

# Verificar logs específicos
./dev.sh logs:backend
./dev.sh logs:frontend
./dev.sh logs:db
```

### 4. Acessar Aplicação

- **Frontend**: http://localhost:3001
- **Backend**: http://localhost:3000
- **Database**: localhost:5432

### 5. Verificar Implementação

```bash
# Verificar se tudo foi configurado corretamente
./verify-onboarding.sh
```

### 6. Testar Fluxos

1. **Registrar novo usuário:**
   - Acesse http://localhost:3001
   - Clique em "Registrar"
   - Crie uma nova conta

2. **Verificar redirecionamento:**
   - Após login, usuário deve ser redirecionado para `/onboarding`
   - Verificar se aparece a tela de boas-vindas

3. **Testar criação de empresa:**
   - Escolher "Criar Nova Empresa"
   - Preencher dados da empresa
   - Verificar criação e redirecionamento para dashboard

4. **Testar códigos e convites:**
   - Como admin, gerar código de empresa
   - Registrar novo usuário e usar código
   - Testar sistema de convites por email

## Próximas Melhorias

### 1. Sistema de Email

- Integração com serviço de email (SendGrid, AWS SES)
- Templates de convite personalizados
- Notificações de expiração

### 2. Interface de Administração

- Painel para gerenciar convites
- Dashboard de códigos ativos
- Relatórios de associações

### 3. Funcionalidades Avançadas

- Convites com múltiplos papéis
- Códigos temporários
- Aprovação manual de associações
- Integração com SSO

## Considerações Técnicas

### 1. Performance

- Índices em tokens e códigos
- Cache de validações
- Paginação de listas

### 2. Escalabilidade

- Limpeza automática de convites expirados
- Arquivamento de códigos antigos
- Monitoramento de uso

### 3. Manutenibilidade

- Testes unitários e integração
- Documentação de API
- Logs estruturados

## Conclusão

O sistema implementado fornece uma base sólida para o gerenciamento de usuários e empresas no FluxoMax, com foco em segurança, usabilidade e escalabilidade. O fluxo de onboarding garante que todos os usuários tenham uma empresa associada antes de acessar funcionalidades críticas do sistema.

Fluxo de Onboarding e Associação de Usuários
```mermaid
graph TD
    A[Usuário se Registra] --> B{Status: no_company}
    B --> C[Redirecionamento para /onboarding]
    
    C --> D[Tela de Boas-vindas]
    D --> E{Escolha do Usuário}
    
    E -->|Criar Empresa| F[Formulário de Criação]
    E -->|Associar-se| G[Formulário de Associação]
    E -->|Ajuda| H[Tela de Ajuda]
    
    F --> I[Validar Dados da Empresa]
    I --> J[Criar Empresa no Backend]
    J --> K[Associar Usuário como Admin]
    K --> L[Status: active]
    L --> M[Redirecionamento para Dashboard]
    
    G --> N{Tipo de Associação}
    N -->|Código| O[Inserir Código da Empresa]
    N -->|Convite| P[Inserir Token do Convite]
    
    O --> Q[Validar Código]
    Q -->|Válido| R[Associar Usuário à Empresa]
    Q -->|Inválido| S[Mostrar Erro]
    
    P --> T[Validar Convite]
    T -->|Válido| U[Aceitar Convite]
    T -->|Inválido| V[Mostrar Erro]
    
    R --> W[Status: active]
    U --> W
    W --> M
    
    S --> G
    V --> G
    H --> D
    
    %% Fluxo de Administração
    X[Admin da Empresa] --> Y{Ação}
    Y -->|Gerar Código| Z[Criar Código de Empresa]
    Y -->|Enviar Convite| AA[Criar Convite por Email]
    
    Z --> BB[Código Disponível para Uso]
    AA --> CC[Convite Enviado]
    
    BB --> O
    CC --> P
    
    %% Estados do Usuário
    DD[UserStatus Enum]
    DD --> EE[no_company: Sem empresa]
    DD --> FF[active: Ativo com empresa]
    DD --> GG[inactive: Inativo]
    DD --> HH[pending: Pendente]
    
    %% Estilos
    classDef userAction fill:#e1f5fe
    classDef systemAction fill:#f3e5f5
    classDef decision fill:#fff3e0
    classDef success fill:#e8f5e8
    classDef error fill:#ffebee
    classDef status fill:#f0f4c3
    
    class A,D,F,G,O,P userAction
    class B,I,J,K,Q,T,U,R,Z,AA,BB,CC systemAction
    class E,N,Y decision
    class L,W,M success
    class S,V error
    class DD,EE,FF,GG,HH status
```

Arquitetura do Sistema de Onboarding
```mermaid
graph TB
    subgraph "Frontend"
        A[Onboarding Page]
        B[CreateCompanyForm]
        C[JoinCompanyForm]
        D[ProtectedRoute]
        E[AuthContext]
    end
    
    subgraph "API Services"
        F[authService]
        G[companyInvitationService]
        H[companyCodeService]
        I[companiesService]
    end
    
    subgraph "Backend Controllers"
        J[AuthController]
        K[CompanyInvitationController]
        L[CompanyCodeController]
        M[CompaniesController]
    end
    
    subgraph "Business Services"
        N[CompanyInvitationService]
        O[CompanyCodeService]
        P[UsersService]
        Q[CompaniesService]
    end
    
    subgraph "Database Models"
        R[(User)]
        S[(Company)]
        T[(CompanyInvitation)]
        U[(CompanyCode)]
        V[(UserCompanyRole)]
        W[(Role)]
    end
    
    subgraph "Security & Middleware"
        X[JwtAuthGuard]
        Y[CompanyRequiredGuard]
    end
    
    %% Frontend Connections
    A --> B
    A --> C
    D --> A
    E --> D
    
    %% API Service Connections
    B --> F
    B --> I
    C --> G
    C --> H
    E --> F
    
    %% Backend API Connections
    F --> J
    G --> K
    H --> L
    I --> M
    
    %% Controller to Service Connections
    J --> P
    K --> N
    L --> O
    M --> Q
    
    %% Service to Database Connections
    N --> T
    N --> R
    N --> S
    N --> V
    N --> W
    
    O --> U
    O --> R
    O --> S
    O --> V
    O --> W
    
    P --> R
    P --> V
    
    Q --> S
    Q --> V
    
    %% Security Connections
    X --> J
    X --> K
    X --> L
    X --> M
    Y --> M
    
    %% Database Relations
    R -.-> V
    S -.-> V
    W -.-> V
    S -.-> T
    W -.-> T
    R -.-> T
    S -.-> U
    W -.-> U
    R -.-> U
    
    %% Styling
    classDef frontend fill:#e3f2fd
    classDef api fill:#f3e5f5
    classDef backend fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef security fill:#ffebee
    
    class A,B,C,D,E frontend
    class F,G,H,I api
    class J,K,L,M,N,O,P,Q backend
    class R,S,T,U,V,W database
    class X,Y security
```